/* استخدام نفس أنماط المتطوعين مع تعديلات للمستفيدين */
@import './Volunteers.css';

/* تخصيصات خاصة بالمستفيدين */
.beneficiaries-container {
  background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
}

.beneficiary-card {
  background: white;
  border: 2px solid #e1e8ed;
  border-radius: 16px;
  padding: 1.5rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.beneficiary-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #2ecc71, #27ae60);
}

.beneficiary-card.active::before {
  background: linear-gradient(90deg, #2ecc71, #27ae60);
}

.beneficiary-card.inactive::before {
  background: linear-gradient(90deg, #e74c3c, #c0392b);
}

.beneficiary-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.beneficiary-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #2ecc71, #27ae60);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  font-weight: 700;
}

.beneficiary-info h3 {
  margin: 0 0 0.25rem 0;
  color: #2c3e50;
  font-size: 1.2rem;
  font-weight: 600;
}

.beneficiary-id {
  margin: 0 0 0.5rem 0;
  color: #7f8c8d;
  font-size: 0.8rem;
}

.beneficiary-details {
  margin-bottom: 1.5rem;
}

.beneficiary-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
}

.beneficiaries-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

.beneficiaries-content {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  padding: 2rem;
}

/* إحصائيات المستفيدين */
.stat-card.families {
  border-left-color: #9b59b6;
}

.stat-card.families .stat-icon {
  background: linear-gradient(135deg, #9b59b6, #8e44ad);
}

/* تصميم متجاوب */
@media (max-width: 768px) {
  .beneficiaries-container {
    padding: 1rem;
  }
  
  .beneficiaries-grid {
    grid-template-columns: 1fr;
  }
  
  .beneficiary-card {
    padding: 1rem;
  }
}
