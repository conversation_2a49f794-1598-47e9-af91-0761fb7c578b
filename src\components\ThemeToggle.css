/* Theme Toggle Styles */
.theme-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
}

.theme-toggle-button {
  background: none;
  border: none;
  cursor: pointer;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s;
  background-color: var(--bg-color);
}

.theme-toggle-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.dark-theme .theme-toggle-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.toggle-icon-container {
  font-size: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Responsive styles */
@media screen and (max-width: 960px) {
  .theme-toggle {
    margin: 1rem 0;
  }
}
