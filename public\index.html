<!doctype html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/x-icon" href="/6.jpg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="دعوة الحق - مؤسسة خيرية تهدف لخدمة المجتمع وتقديم المساعدات للمحتاجين" />
    <meta name="keywords" content="دعوة الحق, مؤسسة خيرية, مساعدات, متطوعين, مستفيدين, خدمة المجتمع" />
    <meta name="author" content="دعوة الحق" />
    <meta property="og:title" content="دعوة الحق - مؤسسة خيرية" />
    <meta property="og:description" content="مؤسسة خيرية تهدف لخدمة المجتمع وتقديم المساعدات للمحتاجين" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://omar-els.github.io/elhaqPro/" />
    <meta property="og:image" content="https://omar-els.github.io/elhaqPro/6.jpg" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="دعوة الحق - مؤسسة خيرية" />
    <meta name="twitter:description" content="مؤسسة خيرية تهدف لخدمة المجتمع وتقديم المساعدات للمحتاجين" />
    <meta name="twitter:image" content="https://omar-els.github.io/elhaqPro/6.jpg" />
    <title>دعوة الحق - مؤسسة خيرية</title>
    
    <!-- GitHub Pages SPA support -->
    <script type="text/javascript">
      // Single Page Apps for GitHub Pages
      // MIT License
      // https://github.com/rafgraph/spa-github-pages
      // This script checks to see if a redirect is present in the query string,
      // converts it back into the correct url and adds it to the
      // browser's history using window.history.replaceState(...),
      // which won't cause the browser to attempt to load the new url.
      // When the single page app is loaded further down in this file,
      // the correct url will be waiting in the browser's history for
      // the single page app to route accordingly.
      (function(l) {
        if (l.search[1] === '/' ) {
          var decoded = l.search.slice(1).split('&').map(function(s) { 
            return s.replace(/~and~/g, '&')
          }).join('?');
          window.history.replaceState(null, null,
              l.pathname.slice(0, -1) + decoded + l.hash
          );
        }
      }(window.location))
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>
