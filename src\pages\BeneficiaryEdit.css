/* BeneficiaryEdit Page Styles */
.beneficiary-edit-container {
  width: 100%;
  max-width: 100%;
  padding: var(--spacing-md);
  overflow-x: hidden;
  box-sizing: border-box;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  flex-wrap: wrap;
  gap: var(--spacing-md);
  width: 100%;
  max-width: 100%;
  overflow: hidden;
  box-sizing: border-box;
}

.page-header h1 {
  margin: 0;
  color: var(--primary-color);
  font-size: 1.8rem;
  flex: 1;
  min-width: 0;
}

.page-header button {
  flex-shrink: 0;
  min-width: 100px;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50vh;
  font-size: 1.2rem;
  color: var(--text-secondary-color);
}

.not-found {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 50vh;
  text-align: center;
  gap: var(--spacing-lg);
}

.not-found h2 {
  color: var(--error-color);
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .beneficiary-edit-container {
    padding: var(--spacing-sm);
  }
  
  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-sm);
  }
  
  .page-header h1 {
    font-size: 1.5rem;
    text-align: center;
  }
  
  .page-header button {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .beneficiary-edit-container {
    padding: var(--spacing-xs);
  }
  
  .page-header h1 {
    font-size: 1.3rem;
  }
  
  .loading {
    font-size: 1rem;
  }
}
