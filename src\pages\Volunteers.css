.volunteers-container {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* Header Styles */
.page-header {
  margin-bottom: 2rem;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  background: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.header-text h1 {
  display: flex;
  align-items: center;
  gap: 1rem;
  color: #2c3e50;
  margin: 0 0 0.5rem 0;
  font-size: 2rem;
  font-weight: 700;
}

.page-icon {
  color: #3498db;
  font-size: 2.5rem;
}

.header-text p {
  color: #7f8c8d;
  margin: 0;
  font-size: 1.1rem;
}

.add-btn {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1.25rem 2.5rem;
  font-size: 1.1rem;
  font-weight: 700;
  border-radius: 15px;
  background: linear-gradient(135deg, #3498db, #2980b9);
  border: none;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 6px 20px rgba(52, 152, 219, 0.3);
  position: relative;
  overflow: hidden;
}

.add-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.add-btn:hover::before {
  left: 100%;
}

.add-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4);
  background: linear-gradient(135deg, #2980b9, #3498db);
}

.add-btn:active {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.add-btn svg {
  font-size: 1.2rem;
  transition: transform 0.3s ease;
}

.add-btn:hover svg {
  transform: rotate(90deg);
}

/* Statistics Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 1.5rem;
  transition: all 0.3s ease;
  border-left: 4px solid;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.stat-card.total {
  border-left-color: #3498db;
}

.stat-card.active {
  border-left-color: #2ecc71;
}

.stat-card.inactive {
  border-left-color: #e74c3c;
}

.stat-card.new {
  border-left-color: #f39c12;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
}

.stat-card.total .stat-icon {
  background: linear-gradient(135deg, #3498db, #2980b9);
}

.stat-card.active .stat-icon {
  background: linear-gradient(135deg, #2ecc71, #27ae60);
}

.stat-card.inactive .stat-icon {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.stat-card.new .stat-icon {
  background: linear-gradient(135deg, #f39c12, #e67e22);
}

.stat-content h3 {
  margin: 0 0 0.5rem 0;
  font-size: 2rem;
  font-weight: 700;
  color: #2c3e50;
}

.stat-content p {
  margin: 0;
  color: #7f8c8d;
  font-size: 0.9rem;
}

/* Controls Section */
.controls-section {
  margin-bottom: 2rem;
}

.search-filter-container {
  background: white;
  padding: 1.5rem;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
}

.search-box {
  position: relative;
  flex: 1;
  min-width: 300px;
}

.search-icon {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #7f8c8d;
  font-size: 1.1rem;
}

.search-box input {
  width: 100%;
  padding: 1rem 3rem 1rem 1rem;
  border: 2px solid #e1e8ed;
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: #f8f9fa;
}

.search-box input:focus {
  outline: none;
  border-color: #3498db;
  background: white;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.filter-controls {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.filter-select {
  padding: 1rem;
  border: 2px solid #e1e8ed;
  border-radius: 12px;
  font-size: 1rem;
  background: #f8f9fa;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 150px;
}

.filter-select:focus {
  outline: none;
  border-color: #3498db;
  background: white;
}

/* Volunteers Grid */
.volunteers-content {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  padding: 2rem;
}

.volunteers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

.volunteer-card {
  background: white;
  border: 2px solid #e1e8ed;
  border-radius: 16px;
  padding: 1.5rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.volunteer-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3498db, #2980b9);
}

.volunteer-card.active::before {
  background: linear-gradient(90deg, #2ecc71, #27ae60);
}

.volunteer-card.inactive::before {
  background: linear-gradient(90deg, #e74c3c, #c0392b);
}

.volunteer-card:hover {
  border-color: #3498db;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.volunteer-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.volunteer-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3498db, #2980b9);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  font-weight: 700;
}

.volunteer-info h3 {
  margin: 0 0 0.25rem 0;
  color: #2c3e50;
  font-size: 1.2rem;
  font-weight: 600;
}

.volunteer-department {
  margin: 0 0 0.5rem 0;
  color: #7f8c8d;
  font-size: 0.9rem;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  color: white;
}

.status-badge.active {
  background: linear-gradient(135deg, #2ecc71, #27ae60);
}

.status-badge.inactive {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.volunteer-details {
  margin-bottom: 1.5rem;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
  color: #34495e;
  font-size: 0.9rem;
}

.detail-item svg {
  color: #7f8c8d;
  font-size: 1rem;
  width: 16px;
}

.volunteer-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
}

.btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-sm {
  padding: 0.5rem;
  font-size: 0.8rem;
}

.btn-primary {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
}

.btn-secondary {
  background: linear-gradient(135deg, #95a5a6, #7f8c8d);
  color: white;
}

.btn-danger {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  color: #7f8c8d;
}

.empty-icon {
  font-size: 4rem;
  color: #bdc3c7;
  margin-bottom: 1rem;
}

.empty-state h3 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
  font-size: 1.5rem;
}

.empty-state p {
  margin: 0 0 2rem 0;
  font-size: 1.1rem;
  line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 768px) {
  .volunteers-container {
    padding: 1rem;
  }
  
  .header-content {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .search-filter-container {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-box {
    min-width: auto;
  }
  
  .volunteers-grid {
    grid-template-columns: 1fr;
  }
  
  .volunteer-card {
    padding: 1rem;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .volunteers-container {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  }
  
  .header-content,
  .search-filter-container,
  .volunteers-content,
  .volunteer-card {
    background: #34495e;
    border-color: #4a5f7a;
  }
  
  .header-text h1,
  .stat-content h3,
  .volunteer-info h3,
  .empty-state h3 {
    color: #ecf0f1;
  }
  
  .header-text p,
  .stat-content p,
  .volunteer-department,
  .detail-item,
  .empty-state p {
    color: #bdc3c7;
  }
  
  .search-box input,
  .filter-select {
    background: #2c3e50;
    border-color: #4a5f7a;
    color: #ecf0f1;
  }
  
  .search-box input:focus,
  .filter-select:focus {
    background: #34495e;
  }
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.modal-content {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  max-width: 90vw;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
}

.volunteer-modal {
  width: 100%;
  max-width: 800px;
}

.details-modal {
  width: 100%;
  max-width: 600px;
}

.delete-modal {
  width: 100%;
  max-width: 400px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem 2rem 1rem 2rem;
  border-bottom: 1px solid #e1e8ed;
}

.modal-header h2 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.5rem;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #7f8c8d;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: #f8f9fa;
  color: #e74c3c;
}

.modal-body {
  padding: 2rem;
}

.modal-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  padding: 1rem 2rem 2rem 2rem;
  border-top: 1px solid #e1e8ed;
}

/* Form Styles */
.volunteer-form {
  padding: 2rem;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 1rem;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: #f8f9fa;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #3498db;
  background: white;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

/* Volunteer Details */
.volunteer-details-content {
  padding: 2rem;
}

.volunteer-profile {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #e1e8ed;
}

.profile-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3498db, #2980b9);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 2rem;
  font-weight: 700;
}

.profile-info h3 {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
  font-size: 1.5rem;
}

.profile-info p {
  margin: 0 0 0.5rem 0;
  color: #7f8c8d;
  font-size: 1rem;
}

.details-grid {
  display: grid;
  gap: 2rem;
}

.detail-section {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 12px;
  border: 1px solid #e1e8ed;
}

.detail-section h4 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
  font-size: 1.1rem;
  font-weight: 600;
  border-bottom: 2px solid #3498db;
  padding-bottom: 0.5rem;
}

.detail-section .detail-item {
  margin-bottom: 0.75rem;
}

.skills-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.skill-tag {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.warning-text {
  color: #e74c3c;
  font-weight: 500;
  margin-top: 0.5rem;
}

/* Responsive Modal */
@media (max-width: 768px) {
  .modal-overlay {
    padding: 0.5rem;
  }

  .modal-content {
    max-width: 100%;
    max-height: 100%;
    border-radius: 12px;
  }

  .modal-header,
  .volunteer-form,
  .volunteer-details-content,
  .modal-body {
    padding: 1rem;
  }

  .modal-actions {
    padding: 1rem;
    flex-direction: column;
  }

  .form-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .volunteer-profile {
    flex-direction: column;
    text-align: center;
  }

  .profile-avatar {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
  }
}

/* Dark Mode for Modals */
@media (prefers-color-scheme: dark) {
  .modal-content {
    background: #34495e;
    border-color: #4a5f7a;
  }

  .modal-header {
    border-bottom-color: #4a5f7a;
  }

  .modal-header h2 {
    color: #ecf0f1;
  }

  .modal-actions {
    border-top-color: #4a5f7a;
  }

  .form-group label {
    color: #ecf0f1;
  }

  .form-group input,
  .form-group select,
  .form-group textarea {
    background: #2c3e50;
    border-color: #4a5f7a;
    color: #ecf0f1;
  }

  .form-group input:focus,
  .form-group select:focus,
  .form-group textarea:focus {
    background: #34495e;
  }

  .detail-section {
    background: #2c3e50;
    border-color: #4a5f7a;
  }

  .detail-section h4 {
    color: #ecf0f1;
  }

  .profile-info h3 {
    color: #ecf0f1;
  }

  .profile-info p {
    color: #bdc3c7;
  }
}

/* Enhanced Modal Styles for New Forms */
.volunteer-form-modal {
  max-width: 900px;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.volunteer-form-modal .modal-header {
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  color: white;
  border-radius: 20px 20px 0 0;
  padding: 2rem;
}

.volunteer-form-modal .modal-header h2 {
  color: white;
  font-size: 1.8rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.volunteer-form-modal .close-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.volunteer-form-modal .close-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.volunteer-form .form-section {
  background: #f8f9fa;
  border-radius: 15px;
  padding: 1.5rem;
  border: 2px solid #e9ecef;
  margin-bottom: 1.5rem;
}

.volunteer-form .form-section h3 {
  margin: 0 0 1.5rem 0;
  color: #2c3e50;
  font-size: 1.3rem;
  font-weight: 600;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid #3498db;
}

.volunteer-form .form-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 2px solid #f0f0f0;
}

.volunteer-form .btn {
  min-width: 150px;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  border-radius: 10px;
}

/* Enhanced Backdrop Blur */
.modal-overlay {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  background: rgba(0, 0, 0, 0.7);
}

/* Form Grid Enhancements */
.volunteer-form .form-grid {
  display: grid;
  gap: 2rem;
}

.volunteer-form .form-group input,
.volunteer-form .form-group select,
.volunteer-form .form-group textarea {
  background: white;
  border: 2px solid #e1e8ed;
  border-radius: 10px;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.volunteer-form .form-group input:focus,
.volunteer-form .form-group select:focus,
.volunteer-form .form-group textarea:focus {
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

/* Responsive Enhancements */
@media (max-width: 768px) {
  .volunteer-form-modal {
    max-width: 95vw;
    max-height: 95vh;
  }

  .volunteer-form-modal .modal-header {
    padding: 1.5rem;
  }

  .volunteer-form {
    padding: 1rem;
  }

  .volunteer-form .form-section {
    padding: 1rem;
  }

  .volunteer-form .form-actions {
    flex-direction: column;
  }

  .volunteer-form .btn {
    width: 100%;
  }
}
