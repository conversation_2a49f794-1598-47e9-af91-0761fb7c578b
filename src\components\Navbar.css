/* Navbar Styles */
.navbar {
  background-color: var(--card-bg);
  box-shadow: 0 2px 4px var(--shadow-color);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.navbar-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 1rem;
  max-width: var(--container-width);
  margin: 0 auto;
  height: 70px;
}

.navbar-logo {
  color: var(--primary-color);
  font-size: 1.5rem;
  font-weight: bold;
  text-decoration: none;
  display: flex;
  align-items: center;
}

.navbar-logo img {
  height: 40px;
  margin-left: 0.5rem;
}

.nav-menu {
  display: flex;
  align-items: center;
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-item {
  margin: 0 0.75rem;
  position: relative;
}

.nav-link {
  color: var(--text-color);
  text-decoration: none;
  font-weight: 500;
  padding: 0.5rem;
  transition: color 0.3s;
  display: flex;
  align-items: center;
}

.nav-link:hover {
  color: var(--primary-color);
}

.notification-badge {
  background-color: var(--error-color);
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  margin-right: 0.5rem;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.user-name {
  font-weight: 500;
}

.logout-btn {
  background: none;
  border: none;
  color: var(--error-color);
  cursor: pointer;
  font-weight: 500;
  padding: 0.25rem 0.5rem;
  transition: color 0.3s;
}

.logout-btn:hover {
  color: #c0392b;
}

.theme-toggle-container {
  margin-right: 1rem;
}

.menu-icon {
  display: none;
  cursor: pointer;
  font-size: 1.5rem;
}

/* Dropdown styles */
.dropdown {
  position: relative;
}

.dropdown-toggle {
  background: none;
  border: none;
  color: var(--text-color);
  cursor: pointer;
  font-weight: 500;
  padding: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1rem;
  font-family: inherit;
}

.dropdown-toggle:hover {
  color: var(--primary-color);
}

.dropdown-toggle .rotate {
  transform: rotate(180deg);
  transition: transform 0.3s;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: var(--card-bg);
  min-width: 180px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  padding: 0.5rem 0;
  z-index: 1000;
  list-style: none;
  margin: 0;
}

.dropdown-menu li {
  padding: 0;
  margin: 0;
}

.dropdown-menu li a,
.dropdown-menu li button {
  display: block;
  padding: 0.75rem 1rem;
  color: var(--text-color);
  text-decoration: none;
  text-align: right;
  width: 100%;
  background: none;
  border: none;
  font-size: 1rem;
  cursor: pointer;
  font-family: inherit;
}

.dropdown-menu li a:hover,
.dropdown-menu li button:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: var(--primary-color);
}

.dropdown-menu li button {
  color: var(--error-color);
}

.dropdown-menu li button:hover {
  color: #c0392b;
}

/* Responsive styles */
@media screen and (max-width: 990px) {
  .navbar-container {
    padding: 0 1rem;
  }
  
  .menu-icon {
    display: block;
    position: absolute;
    left: 1rem;
    top: 1.25rem;
    font-size: 1.8rem;
    cursor: pointer;
    color: var(--text-color);
  }
  
  .nav-menu {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: calc(100vh - 70px);
    position: absolute;
    top: 70px;
    right: -100%;
    opacity: 1;
    transition: all 0.5s ease;
    background-color: var(--card-bg);
    padding: 1rem 0;
    z-index: 999;
  }
  
  .nav-menu.active {
    right: 0;
    opacity: 1;
    transition: all 0.5s ease;
    box-shadow: 0 4px 8px var(--shadow-color);
  }
  
  .nav-item {
    margin: 0;
    width: 100%;
    padding: 1rem 2rem;
    border-bottom: 1px solid var(--border-color);
  }
  
  .nav-link {
    display: block;
    width: 100%;
    text-align: right;
    padding: 0.5rem 0;
  }
  
  .user-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .theme-toggle-container {
    margin: 1rem 2rem;
    width: 100%;
    display: flex;
    justify-content: flex-start;
  }
}

@media screen and (max-width: 480px) {
  .navbar-logo {
    font-size: 1.2rem;
  }
  
  .navbar-container {
    height: 60px;
  }
  
  .nav-menu {
    top: 60px;
    height: calc(100vh - 60px);
  }
  
  .menu-icon {
    top: 1rem;
    font-size: 1.5rem;
  }
}


