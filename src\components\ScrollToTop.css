.scroll-to-top-button {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: var(--bg-color);
  color: var(--text-secondary-color);
  border: 1px solid var(--border-color);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1.2rem;
  cursor: pointer;
  z-index: 999;
  transition: all 0.3s ease;
  opacity: 0.7;
}

.scroll-to-top-button.active {
  background-color: var(--primary-color);
  color: white;
  border: none;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  opacity: 1;
}

.scroll-to-top-button:hover {
  opacity: 1;
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.scroll-to-top-button.active:hover {
  background-color: #2980b9;
}

/* Position relative to FAB */
.fab-active .scroll-to-top-button {
  right: 5.5rem;
  bottom: 2rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .scroll-to-top-button {
    bottom: 1.5rem;
    right: 1.5rem;
    width: 45px;
    height: 45px;
    font-size: 1rem;
  }

  .fab-active .scroll-to-top-button {
    right: 4.5rem;
    bottom: 1.5rem;
  }
}
