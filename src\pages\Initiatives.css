/* Initiatives Page Styles */
.initiatives-container {
  padding: var(--spacing-md) 0;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  flex-wrap: wrap;
  gap: var(--spacing-md);
}

.page-header h1 {
  margin: 0;
  color: var(--text-color);
}

.initiatives-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: var(--spacing-lg);
}

.initiative-card {
  background-color: var(--card-bg);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px var(--shadow-color);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.initiative-card h3 {
  margin-top: 0;
  color: var(--primary-color);
  font-size: 1.25rem;
}

.initiative-date {
  color: var(--text-secondary-color);
  font-size: 0.9rem;
  margin-bottom: var(--spacing-md);
}

.initiative-description {
  flex-grow: 1;
  margin-bottom: var(--spacing-md);
}

.initiative-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--spacing-md);
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

.stat {
  background-color: rgba(0, 0, 0, 0.05);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: 4px;
  flex: 1;
  min-width: 120px;
  text-align: center;
}

.stat-label {
  display: block;
  font-size: 0.8rem;
  color: var(--text-secondary-color);
  margin-bottom: 0.25rem;
}

.stat-value {
  font-weight: bold;
  font-size: 1.1rem;
}

.initiative-details {
  background-color: rgba(0, 0, 0, 0.03);
  padding: var(--spacing-md);
  border-radius: 4px;
  margin-bottom: var(--spacing-md);
}

.initiative-details h4 {
  margin-top: 0;
  margin-bottom: var(--spacing-sm);
}

.beneficiaries-list {
  margin: 0;
  padding-right: var(--spacing-lg);
}

.beneficiaries-list li {
  margin-bottom: var(--spacing-xs);
}

.card-actions {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
  margin-top: auto;
}

.card-actions button {
  flex: 1;
  min-width: 80px;
}

.loading-message,
.error-message,
.no-results {
  text-align: center;
  padding: var(--spacing-xl);
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  box-shadow: 0 2px 8px var(--shadow-color);
}

.error-message {
  color: var(--error-color);
}

/* Form styles */
.initiative-form-container {
  background-color: var(--card-bg);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px var(--shadow-color);
}

/* Responsive styles */
@media (max-width: 768px) {
  .initiatives-grid {
    grid-template-columns: 1fr;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .page-header button {
    width: 100%;
  }
  
  .card-actions {
    flex-direction: column;
  }
  
  .card-actions button {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .initiative-stats {
    flex-direction: column;
  }
  
  .stat {
    width: 100%;
  }
}

