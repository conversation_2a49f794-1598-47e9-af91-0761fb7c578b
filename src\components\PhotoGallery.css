.photo-gallery {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

/* Header Styles */
.gallery-header {
  text-align: center;
  margin-bottom: 3rem;
}

.header-content {
  margin-bottom: 2rem;
}

.header-icon {
  font-size: 3rem;
  color: #2ecc71;
  margin-bottom: 1rem;
}

.gallery-header h2 {
  font-size: 2.5rem;
  color: #2c3e50;
  margin: 0 0 1rem 0;
  font-weight: 700;
}

.gallery-header p {
  font-size: 1.2rem;
  color: #7f8c8d;
  margin: 0;
  line-height: 1.6;
}

/* Search Container */
.search-container {
  max-width: 600px;
  margin: 0 auto;
}

.search-box {
  position: relative;
  display: flex;
  align-items: center;
  background: white;
  border: 2px solid #e1e8ed;
  border-radius: 25px;
  padding: 0.75rem 1rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.search-box:focus-within {
  border-color: #2ecc71;
  box-shadow: 0 4px 20px rgba(46, 204, 113, 0.2);
}

.search-icon {
  color: #7f8c8d;
  margin-left: 0.5rem;
  font-size: 1.1rem;
}

.search-box input {
  flex: 1;
  border: none;
  outline: none;
  font-size: 1rem;
  padding: 0.5rem;
  background: transparent;
}

.clear-search {
  background: none;
  border: none;
  color: #7f8c8d;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.clear-search:hover {
  background: #f8f9fa;
  color: #e74c3c;
}

.search-results {
  text-align: center;
  margin-top: 1rem;
  color: #7f8c8d;
  font-size: 0.9rem;
}

/* Photos Grid */
.photos-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.photo-card {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
}

.photo-card:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.photo-container {
  position: relative;
  height: 250px;
  overflow: hidden;
}

.photo-container img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.photo-card:hover .photo-container img {
  transform: scale(1.05);
}

.photo-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.photo-card:hover .photo-overlay {
  opacity: 1;
}

.expand-icon {
  color: white;
  font-size: 2rem;
}

/* Photo Info */
.photo-info {
  padding: 1.5rem;
}

.photo-info h3 {
  font-size: 1.3rem;
  color: #2c3e50;
  margin: 0 0 0.5rem 0;
  font-weight: 600;
}

.photo-description {
  color: #7f8c8d;
  font-size: 0.9rem;
  line-height: 1.5;
  margin: 0 0 1rem 0;
}

.photo-meta {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.8rem;
  color: #34495e;
}

.meta-item svg {
  color: #2ecc71;
  font-size: 0.9rem;
}

.photo-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  background: none;
  border: none;
  color: #7f8c8d;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 8px;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.action-btn:hover {
  background: #f8f9fa;
  color: #2c3e50;
}

.like-btn:hover {
  color: #e74c3c;
}

.share-btn:hover {
  color: #3498db;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  color: #7f8c8d;
}

.empty-icon {
  font-size: 4rem;
  color: #bdc3c7;
  margin-bottom: 1rem;
}

.empty-state h3 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
  font-size: 1.5rem;
}

.empty-state p {
  margin: 0 0 2rem 0;
  font-size: 1.1rem;
  line-height: 1.6;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.btn-primary {
  background: linear-gradient(135deg, #2ecc71, #27ae60);
  color: white;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Lightbox */
.lightbox-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
}

.lightbox-content {
  background: white;
  border-radius: 16px;
  max-width: 90vw;
  max-height: 90vh;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
}

.close-btn {
  position: absolute;
  top: 1rem;
  left: 1rem;
  background: rgba(0, 0, 0, 0.5);
  border: none;
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: rgba(0, 0, 0, 0.7);
}

.lightbox-image {
  position: relative;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
}

.lightbox-image img {
  max-width: 100%;
  max-height: 60vh;
  object-fit: contain;
}

.nav-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.5);
  border: none;
  color: white;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 1.2rem;
  transition: all 0.3s ease;
}

.nav-btn:hover {
  background: rgba(0, 0, 0, 0.7);
}

.prev-btn {
  right: 1rem;
}

.next-btn {
  left: 1rem;
}

.lightbox-info {
  padding: 2rem;
  background: white;
}

.lightbox-info h3 {
  font-size: 1.5rem;
  color: #2c3e50;
  margin: 0 0 1rem 0;
  font-weight: 600;
}

.lightbox-info p {
  color: #7f8c8d;
  line-height: 1.6;
  margin: 0 0 1.5rem 0;
}

.lightbox-meta {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 2rem;
}

.meta-row {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #34495e;
}

.meta-row svg {
  color: #2ecc71;
  font-size: 1rem;
}

.lightbox-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

/* Responsive Design */
@media (max-width: 768px) {
  .photo-gallery {
    padding: 1rem;
  }
  
  .gallery-header h2 {
    font-size: 2rem;
  }
  
  .photos-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .photo-card {
    margin: 0 auto;
    max-width: 400px;
  }
  
  .lightbox-overlay {
    padding: 1rem;
  }
  
  .lightbox-content {
    max-width: 95vw;
    max-height: 95vh;
  }
  
  .lightbox-info {
    padding: 1rem;
  }
  
  .lightbox-actions {
    flex-direction: column;
  }
  
  .nav-btn {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }
  
  .prev-btn {
    right: 0.5rem;
  }
  
  .next-btn {
    left: 0.5rem;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .photo-gallery {
    background: #2c3e50;
    color: #ecf0f1;
  }
  
  .gallery-header h2 {
    color: #ecf0f1;
  }
  
  .photo-card {
    background: #34495e;
  }
  
  .search-box {
    background: #34495e;
    border-color: #4a5f7a;
    color: #ecf0f1;
  }
  
  .lightbox-content {
    background: #34495e;
  }
  
  .lightbox-info {
    background: #34495e;
    color: #ecf0f1;
  }
  
  .lightbox-info h3 {
    color: #ecf0f1;
  }
}
