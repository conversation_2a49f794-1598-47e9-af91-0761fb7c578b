.thank-you-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  position: relative;
  overflow: hidden;
}

.thank-you-content {
  background: white;
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
  padding: 3rem;
  max-width: 800px;
  width: 100%;
  text-align: center;
  position: relative;
  z-index: 2;
}

.success-icon {
  font-size: 5rem;
  color: #28a745;
  margin-bottom: 1rem;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.thank-you-content h1 {
  font-size: 3rem;
  color: #2c3e50;
  margin: 0 0 0.5rem 0;
  font-weight: 700;
}

.thank-you-content h2 {
  font-size: 1.5rem;
  color: #667eea;
  margin: 0 0 2rem 0;
  font-weight: 600;
}

.main-message {
  font-size: 1.2rem;
  color: #34495e;
  line-height: 1.8;
  margin-bottom: 3rem;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 10px;
  border-left: 4px solid #667eea;
}

.next-steps {
  margin-bottom: 3rem;
  text-align: right;
}

.next-steps h3 {
  color: #2c3e50;
  font-size: 1.5rem;
  margin-bottom: 2rem;
  text-align: center;
}

.steps-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.step-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 10px;
  transition: all 0.3s ease;
}

.step-item:hover {
  transform: translateX(-5px);
  box-shadow: 0 5px 20px rgba(102, 126, 234, 0.2);
}

.step-number {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 1.2rem;
  flex-shrink: 0;
}

.step-content h4 {
  color: #2c3e50;
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.step-content p {
  color: #7f8c8d;
  margin: 0;
  line-height: 1.6;
}

.contact-info {
  margin-bottom: 3rem;
  padding: 2rem;
  background: #f8f9fa;
  border-radius: 15px;
}

.contact-info h3 {
  color: #2c3e50;
  font-size: 1.3rem;
  margin-bottom: 1rem;
}

.contact-info p {
  color: #7f8c8d;
  margin-bottom: 1.5rem;
}

.contact-methods {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.contact-method {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: white;
  border: 2px solid #e1e8ed;
  border-radius: 25px;
  color: #2c3e50;
  text-decoration: none;
  transition: all 0.3s ease;
  font-weight: 500;
}

.contact-method:hover {
  border-color: #667eea;
  background: #667eea;
  color: white;
  transform: translateY(-2px);
}

.social-follow {
  margin-bottom: 3rem;
}

.social-follow h3 {
  color: #2c3e50;
  font-size: 1.3rem;
  margin-bottom: 1.5rem;
}

.social-links {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.social-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 1.5rem;
  border-radius: 10px;
  color: white;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
}

.social-link:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
}

.social-link.facebook {
  background: linear-gradient(135deg, #1877f2, #42a5f5);
}

.social-link.twitter {
  background: linear-gradient(135deg, #1da1f2, #42a5f5);
}

.social-link.whatsapp {
  background: linear-gradient(135deg, #25d366, #128c7e);
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  border: none;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  cursor: pointer;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.btn-secondary {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
}

/* Decorative Elements */
.decorative-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

.floating-heart {
  position: absolute;
  color: rgba(255, 255, 255, 0.3);
  font-size: 2rem;
  animation: float 6s ease-in-out infinite;
}

.heart-1 {
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.heart-2 {
  top: 20%;
  right: 15%;
  animation-delay: 2s;
}

.heart-3 {
  bottom: 15%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(10deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .thank-you-container {
    padding: 1rem;
  }
  
  .thank-you-content {
    padding: 2rem 1rem;
  }
  
  .thank-you-content h1 {
    font-size: 2rem;
  }
  
  .thank-you-content h2 {
    font-size: 1.2rem;
  }
  
  .main-message {
    font-size: 1rem;
    padding: 1rem;
  }
  
  .steps-list {
    gap: 1rem;
  }
  
  .step-item {
    padding: 1rem;
    flex-direction: column;
    text-align: center;
  }
  
  .contact-methods,
  .social-links,
  .action-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .contact-method,
  .social-link,
  .btn {
    width: 100%;
    max-width: 250px;
    justify-content: center;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .thank-you-content {
    background: #34495e;
    color: #ecf0f1;
  }
  
  .thank-you-content h1,
  .next-steps h3,
  .contact-info h3,
  .social-follow h3 {
    color: #ecf0f1;
  }
  
  .main-message,
  .step-item,
  .contact-info {
    background: #2c3e50;
    color: #bdc3c7;
  }
  
  .step-content h4 {
    color: #ecf0f1;
  }
  
  .contact-method {
    background: #2c3e50;
    border-color: #4a5f7a;
    color: #ecf0f1;
  }
  
  .contact-method:hover {
    background: #667eea;
    color: white;
  }
}
