/* Home Page Styles */
.home-container {
  padding: var(--spacing-md) 0;
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
  box-sizing: border-box;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  flex-wrap: wrap;
  gap: var(--spacing-md);
}

.page-header h1 {
  margin: 0;
  color: var(--primary-color);
}

.add-form-container {
  margin-bottom: 2rem;
  overflow: hidden;
}

.filters-container {
  margin-bottom: 2rem;
}

.filters-container h2 {
  margin-top: 0;
  margin-bottom: 1.5rem;
  color: var(--primary-color);
}

.filters-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
  margin-bottom: 1.5rem;
  width: 100%;
  max-width: 100%;
  overflow: hidden;
  box-sizing: border-box;
}

.filter-group {
  margin-bottom: 1rem;
}

.filter-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: bold;
}

.filter-group input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--bg-color);
  color: var(--text-color);
}

.beneficiaries-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  width: 100%;
  max-width: 100%;
  overflow: hidden;
  box-sizing: border-box;
}

.loading-message,
.no-results {
  text-align: center;
  padding: 2rem;
  font-size: 1.2rem;
  color: var(--text-color);
}

.error-message {
  background-color: rgba(231, 76, 60, 0.1);
  border: 1px solid var(--error-color);
  color: var(--error-color);
  padding: 1rem;
  border-radius: 4px;
  margin-bottom: 1.5rem;
  text-align: center;
}

/* Beneficiary Card Styles */
.beneficiary-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.card-header {
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--border-color);
}

.header-content {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.profile-image-container {
  flex-shrink: 0;
}

.profile-image {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--primary-color);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-text {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 1.2rem;
}

.priority-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: bold;
}

.priority-high {
  background-color: rgba(231, 76, 60, 0.2);
  color: #c0392b;
}

.priority-medium {
  background-color: rgba(243, 156, 18, 0.2);
  color: #d35400;
}

.priority-low {
  background-color: rgba(46, 204, 113, 0.2);
  color: #27ae60;
}

.card-content {
  flex: 1;
}

.info-row {
  display: flex;
  margin-bottom: 0.5rem;
}

.info-label {
  font-weight: bold;
  min-width: 120px;
}

.details-section {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px dashed var(--border-color);
}

.image-row {
  margin-top: 1rem;
}

.id-image {
  display: block;
  max-width: 100%;
  height: auto;
  margin-top: 0.5rem;
  border-radius: 4px;
  border: 1px solid var(--border-color);
}

.notes-text {
  white-space: pre-line;
  color: var(--text-secondary-color);
  font-style: italic;
}

.children-section {
  margin-top: 1.5rem;
  padding: 1rem;
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 4px;
}

.children-section h4 {
  margin-top: 0;
  margin-bottom: 1rem;
  font-size: 1rem;
  color: var(--primary-color);
}

.children-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.child-item {
  padding: 0.75rem;
  margin-bottom: 0.5rem;
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.child-name {
  font-weight: bold;
  margin-bottom: 0.25rem;
}

.child-info {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  font-size: 0.85rem;
  color: var(--text-secondary-color);
}

.child-info span {
  padding: 0.15rem 0.5rem;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 12px;
}

.health-status {
  color: var(--error-color) !important;
  font-weight: 500;
}

.card-actions {
  display: flex;
  justify-content: space-between;
  gap: 0.5rem;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color);
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--text-color);
}

/* Responsive styles */
@media (max-width: 992px) {
  .filters-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .page-header h1 {
    margin-bottom: 1rem;
  }

  .card-actions {
    flex-direction: column;
  }

  .card-actions button {
    width: 100%;
    margin-bottom: 0.5rem;
  }
}

@media (max-width: 768px) {
  .home-container {
    overflow-x: hidden;
    padding: var(--spacing-sm) 0;
  }

  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
  }

  .page-header h1 {
    text-align: center;
    font-size: 1.5rem;
  }

  .page-header button {
    width: 100%;
    justify-content: center;
    min-height: 48px;
  }

  .filters-container {
    margin-bottom: var(--spacing-md);
  }

  .filters-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-sm);
  }

  .filter-group input {
    min-height: 44px;
    font-size: 16px;
  }

  .beneficiaries-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .header-content {
    gap: 0.75rem;
  }

  .add-form-container {
    margin-bottom: var(--spacing-md);
  }
}

@media (max-width: 576px) {
  .home-container {
    padding: var(--spacing-xs) 0;
  }

  .page-header h1 {
    font-size: 1.3rem;
  }

  .filters-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-xs);
  }

  .filter-group input {
    min-height: 48px;
    font-size: 16px;
    padding: 1rem;
  }

  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .header-text {
    width: 100%;
  }

  .beneficiary-card {
    overflow: hidden;
    margin-bottom: var(--spacing-sm);
  }

  .card-details {
    overflow-x: auto;
  }

  .card-actions {
    flex-direction: column;
    gap: var(--spacing-xs);
  }

  .card-actions button {
    width: 100%;
    min-height: 44px;
    justify-content: center;
  }

  .filters-container .btn {
    width: 100%;
    min-height: 48px;
    margin-top: var(--spacing-sm);
  }
}

/* Image styles */
.image-row {
  display: flex;
  flex-direction: column;
  margin-bottom: 1rem;
}

.image-row .info-label {
  margin-bottom: 0.5rem;
}

.id-image {
  max-width: 200px;
  max-height: 150px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-top: 0.5rem;
}

.profile-image-large {
  max-width: 150px;
  max-height: 150px;
  border-radius: 8px;
  object-fit: cover;
  border: 2px solid var(--primary-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-top: 0.5rem;
}

/* Gallery Section */
.gallery-section {
  margin-top: 4rem;
  padding: 2rem 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 20px;
  margin-bottom: 2rem;
}

/* Dark mode styles */
@media (prefers-color-scheme: dark) {
  .gallery-section {
    background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
  }
}

