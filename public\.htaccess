# GitHub Pages MIME Type Configuration
# Force correct MIME types for JavaScript modules
<FilesMatch "\.(js|mjs)$">
    Header set Content-Type "application/javascript; charset=utf-8"
    Header set X-Content-Type-Options "nosniff"
</FilesMatch>

<FilesMatch "\.css$">
    Header set Content-Type "text/css; charset=utf-8"
</FilesMatch>

<FilesMatch "\.json$">
    Header set Content-Type "application/json; charset=utf-8"
</FilesMatch>

<FilesMatch "\.svg$">
    Header set Content-Type "image/svg+xml; charset=utf-8"
</FilesMatch>

# Fallback MIME types
AddType application/javascript .js
AddType application/javascript .mjs
AddType text/css .css
AddType application/json .json
AddType image/svg+xml .svg
AddType application/wasm .wasm

# Enable compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Cache control
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType text/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
</IfModule>

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
</IfModule>

# Fallback for SPA routing
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteBase /elhaqPro/
    RewriteRule ^index\.html$ - [L]
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule . /elhaqPro/index.html [L]
</IfModule>
