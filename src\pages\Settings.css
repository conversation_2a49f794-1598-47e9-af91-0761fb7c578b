.settings-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-md);
}

.page-header {
  margin-bottom: var(--spacing-lg);
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.page-header h1 {
  color: var(--primary-color);
  margin-bottom: var(--spacing-sm);
}

.settings-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-sm);
  background-color: rgba(52, 152, 219, 0.1);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius);
  animation: pulse 2s infinite;
}

.status-badge {
  background-color: var(--primary-color);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.header-actions {
  display: flex;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-sm);
}

/* Preview Banner */
.preview-banner {
  background-color: rgba(46, 204, 113, 0.1);
  border: 1px solid var(--secondary-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-sm) var(--spacing-md);
  margin-bottom: var(--spacing-md);
  display: flex;
  justify-content: center;
  align-items: center;
}

.preview-message {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--secondary-color);
  font-weight: 500;
}

.preview-message svg {
  font-size: 1.2rem;
}

/* Settings Layout */
.settings-content {
  display: flex;
  gap: var(--spacing-lg);
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  box-shadow: 0 2px 8px var(--shadow-color);
  overflow: hidden;
}

.settings-sidebar {
  width: 200px;
  background-color: var(--bg-color);
  border-left: 1px solid var(--border-color);
}

.settings-tab {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  width: 100%;
  padding: var(--spacing-md);
  background: none;
  border: none;
  text-align: right;
  cursor: pointer;
  color: var(--text-color);
  transition: all 0.3s ease;
  border-bottom: 1px solid var(--border-color);
}

.settings-tab:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.settings-tab.active {
  color: var(--primary-color);
  background-color: rgba(52, 152, 219, 0.05);
  border-right: 4px solid var(--primary-color);
}

.settings-tab svg {
  font-size: 1.2rem;
}

.settings-panel {
  flex: 1;
  padding: var(--spacing-lg);
}

/* Settings Section */
.settings-section {
  margin-bottom: var(--spacing-lg);
}

.settings-section h2 {
  margin-top: 0;
  margin-bottom: var(--spacing-md);
  color: var(--text-color);
  font-size: 1.5rem;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: var(--spacing-sm);
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.setting-label {
  font-weight: 500;
  color: var(--text-color);
}

.setting-control {
  min-width: 200px;
}

/* Form Controls */
.setting-control input[type="text"],
.setting-control input[type="email"],
.setting-control input[type="number"],
.setting-control select {
  width: 100%;
  padding: var(--spacing-sm);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background-color: var(--bg-color);
  color: var(--text-color);
}

/* Toggle Switch */
.toggle-switch {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.toggle-switch input[type="checkbox"] {
  height: 0;
  width: 0;
  visibility: hidden;
  position: absolute;
}

.toggle-switch label {
  cursor: pointer;
  width: 50px;
  height: 25px;
  background: var(--border-color);
  display: block;
  border-radius: 25px;
  position: relative;
}

.toggle-switch label:after {
  content: '';
  position: absolute;
  top: 3px;
  right: 3px;
  width: 19px;
  height: 19px;
  background: #fff;
  border-radius: 19px;
  transition: 0.3s;
}

.toggle-switch input:checked + label {
  background: var(--primary-color);
}

.toggle-switch input:checked + label:after {
  right: calc(100% - 3px);
  transform: translateX(100%);
}

.toggle-switch span {
  font-size: 0.9rem;
  color: var(--text-secondary-color);
}

/* Theme Toggle */
.theme-toggle {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  background-color: var(--bg-color);
  color: var(--text-color);
  cursor: pointer;
  transition: all 0.3s ease;
}

.theme-toggle.light {
  background-color: rgba(241, 196, 15, 0.1);
}

.theme-toggle.dark {
  background-color: rgba(52, 73, 94, 0.1);
}

.theme-toggle svg {
  font-size: 1.2rem;
}

/* Settings Actions */
.settings-actions {
  margin-top: var(--spacing-lg);
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
}

.settings-actions-sidebar {
  margin-top: auto;
  padding: var(--spacing-md);
  border-top: 1px solid var(--border-color);
}

.btn-reset {
  background-color: transparent;
  color: var(--text-secondary-color);
  border: 1px solid var(--border-color);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  transition: all 0.3s ease;
}

.btn-reset:hover {
  background-color: rgba(231, 76, 60, 0.1);
  color: var(--error-color);
  border-color: var(--error-color);
}

.btn-danger {
  background-color: var(--error-color);
  color: white;
}

.btn-danger:hover {
  background-color: #c0392b;
}

/* Confirm Dialog */
.confirm-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.confirm-dialog {
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
  width: 90%;
  max-width: 500px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.confirm-dialog h3 {
  margin-top: 0;
  color: var(--error-color);
}

.confirm-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-lg);
}

/* Theme Options */
.theme-options {
  display: flex;
  gap: var(--spacing-md);
}

.theme-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  background-color: transparent;
  cursor: pointer;
  transition: all 0.3s ease;
}

.theme-option.active {
  border-color: var(--primary-color);
  background-color: rgba(52, 152, 219, 0.1);
}

.theme-preview {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1.5rem;
}

.light-preview {
  background-color: #f5f5f5;
  color: #f39c12;
}

.dark-preview {
  background-color: #2c3e50;
  color: #f1c40f;
}

/* Color Pickers */
.color-pickers {
  display: flex;
  gap: var(--spacing-md);
}

.color-picker {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.color-picker label {
  font-size: 0.9rem;
  color: var(--text-secondary-color);
}

.color-preview {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  position: relative;
  cursor: pointer;
  border: 2px solid var(--border-color);
}

.color-preview input {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
}

/* Font Size Options */
.font-size-options {
  display: flex;
  gap: var(--spacing-md);
}

.font-size-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  background-color: transparent;
  cursor: pointer;
  transition: all 0.3s ease;
}

.font-size-option.active {
  border-color: var(--primary-color);
  background-color: rgba(52, 152, 219, 0.1);
}

.font-small {
  font-size: 0.8rem;
}

.font-medium {
  font-size: 1rem;
}

.font-large {
  font-size: 1.2rem;
}

/* Setting Preview */
.setting-preview {
  margin-top: var(--spacing-lg);
  border-top: 1px solid var(--border-color);
  padding-top: var(--spacing-md);
}

.setting-preview h3 {
  margin-top: 0;
  margin-bottom: var(--spacing-md);
  color: var(--text-color);
  font-size: 1.2rem;
}

.preview-box {
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: 0 2px 8px var(--shadow-color);
}

.preview-box.light {
  background-color: #ffffff;
  color: #333333;
}

.preview-box.dark {
  background-color: #2c3e50;
  color: #ecf0f1;
}

.preview-header {
  padding: var(--spacing-sm) var(--spacing-md);
  color: white;
}

.preview-title {
  font-weight: 500;
}

.preview-content {
  padding: var(--spacing-md);
}

.preview-text {
  margin-bottom: var(--spacing-md);
}

.preview-button {
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: var(--border-radius);
  color: white;
  cursor: pointer;
}

/* Range Slider */
.range-slider {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.range-slider input {
  flex: 1;
}

.range-value {
  min-width: 60px;
  text-align: center;
  font-weight: 500;
  color: var(--primary-color);
}

/* Setting Info */
.setting-info {
  background-color: rgba(52, 152, 219, 0.05);
  border-radius: var(--border-radius);
  padding: var(--spacing-sm) var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.setting-info p {
  margin: 0;
  font-size: 0.9rem;
  color: var(--text-secondary-color);
}

.warning-text {
  color: var(--error-color);
}

/* Notification Preview */
.notification-preview {
  background-color: var(--bg-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-md);
  box-shadow: 0 2px 4px var(--shadow-color);
}

.notification-item {
  display: flex;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-md);
  box-shadow: 0 1px 3px var(--shadow-color);
}

.notification-item.disabled {
  opacity: 0.5;
  filter: grayscale(1);
}

.notification-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
}

.notification-icon.success {
  background-color: var(--secondary-color);
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-weight: 500;
  margin-bottom: var(--spacing-xs);
}

.notification-message {
  font-size: 0.9rem;
  color: var(--text-secondary-color);
}

/* Account Preview */
.account-header {
  display: flex;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.avatar-container {
  flex-shrink: 0;
}

.avatar-preview {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background-color: var(--bg-color);
  border: 2px solid var(--border-color);
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 2rem;
  color: var(--text-secondary-color);
  position: relative;
  background-size: cover;
  background-position: center;
}

.avatar-upload-btn {
  position: absolute;
  bottom: 0;
  right: 0;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  font-size: 0.7rem;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.avatar-preview:hover .avatar-upload-btn {
  opacity: 1;
}

.account-info {
  flex: 1;
}

/* Language Options */
.language-options {
  display: flex;
  gap: var(--spacing-md);
}

.language-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  background-color: transparent;
  cursor: pointer;
  transition: all 0.3s ease;
}

.language-option.active {
  border-color: var(--primary-color);
  background-color: rgba(52, 152, 219, 0.1);
}

.language-flag {
  font-size: 1.5rem;
}

/* Account Card */
.account-card {
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: 0 2px 4px var(--shadow-color);
}

.account-card-header {
  background-color: var(--primary-color);
  padding: var(--spacing-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  color: white;
}

.account-card-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1.2rem;
  background-size: cover;
  background-position: center;
}

.account-card-name {
  font-weight: 500;
  font-size: 1.1rem;
}

.account-card-body {
  padding: var(--spacing-md);
}

.account-card-email {
  margin-bottom: var(--spacing-sm);
  color: var(--text-secondary-color);
}

.account-card-language {
  font-size: 0.9rem;
}

/* Privacy Status */
.privacy-status {
  background-color: var(--bg-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-md);
}

.privacy-status-item {
  display: flex;
  justify-content: space-between;
  padding: var(--spacing-sm) 0;
  border-bottom: 1px solid var(--border-color);
}

.privacy-status-item:last-child {
  border-bottom: none;
}

.status-label {
  color: var(--text-secondary-color);
}

.status-value {
  font-weight: 500;
}

.status-value.active {
  color: var(--secondary-color);
}

.status-value.inactive {
  color: var(--text-secondary-color);
}

.status-value.warning {
  color: #f39c12;
}

/* Export Format Options */
.export-format-options {
  display: flex;
  gap: var(--spacing-md);
}

.export-format-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  background-color: transparent;
  cursor: pointer;
  transition: all 0.3s ease;
}

.export-format-option.active {
  border-color: var(--primary-color);
  background-color: rgba(52, 152, 219, 0.1);
}

.export-format-option svg {
  font-size: 1.5rem;
  color: var(--primary-color);
}

/* Data Management */
.data-management {
  background-color: var(--bg-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-md);
}

.data-actions {
  display: flex;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
}

.data-status {
  margin-top: var(--spacing-md);
}

/* Animation */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(52, 152, 219, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(52, 152, 219, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(52, 152, 219, 0);
  }
}

/* Preview Mode */
.preview-mode {
  --font-size-base: var(--font-size-preview);
  --primary-color: var(--primary-color-preview);
  --secondary-color: var(--secondary-color-preview);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .settings-content {
    flex-direction: column;
  }

  .settings-sidebar {
    width: 100%;
    border-left: none;
    border-bottom: 1px solid var(--border-color);
  }

  .settings-tab {
    padding: var(--spacing-sm) var(--spacing-md);
  }

  .setting-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }

  .setting-control {
    width: 100%;
  }

  .theme-options,
  .font-size-options,
  .language-options,
  .export-format-options,
  .color-pickers {
    flex-wrap: wrap;
  }

  .account-header {
    flex-direction: column;
    align-items: center;
  }
}
