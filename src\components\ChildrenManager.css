.children-manager {
  margin-top: 1.5rem;
  padding: 1rem;
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.children-manager h3 {
  margin-top: 0;
  margin-bottom: 1rem;
  color: var(--primary-color);
  font-size: 1.2rem;
}

.children-list {
  list-style: none;
  padding: 0;
  margin: 0 0 1.5rem 0;
}

.child-item {
  margin-bottom: 0.75rem;
  padding: 0.75rem;
  border-radius: var(--border-radius);
  background-color: rgba(255, 255, 255, 0.5);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.child-item:hover {
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
}

.child-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.child-details {
  flex: 1;
}

.child-details h4 {
  margin: 0 0 0.25rem 0;
  font-size: 1rem;
}

.child-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  font-size: 0.85rem;
  color: var(--text-secondary-color);
}

.child-meta span {
  padding: 0.15rem 0.5rem;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 12px;
}

.health-status {
  color: var(--error-color) !important;
  font-weight: 500;
}

.child-actions {
  display: flex;
  gap: 0.5rem;
}

.btn-icon {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1rem;
  padding: 0.25rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.btn-icon:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.btn-icon.edit:hover {
  background-color: rgba(52, 152, 219, 0.1);
}

.btn-icon.delete:hover {
  background-color: rgba(231, 76, 60, 0.1);
}

.child-edit-form {
  padding: 0.5rem 0;
}

.form-row {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 0.75rem;
}

.form-group {
  flex: 1;
  min-width: 150px;
}

.form-group label {
  display: block;
  margin-bottom: 0.25rem;
  font-size: 0.9rem;
  color: var(--text-secondary-color);
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 0.9rem;
}

.form-group input.error,
.form-group select.error {
  border-color: var(--error-color);
}

.error-text {
  display: block;
  color: var(--error-color);
  font-size: 0.8rem;
  margin-top: 0.25rem;
}

.edit-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.btn-sm {
  padding: 0.25rem 0.75rem;
  font-size: 0.85rem;
}

.add-child-form {
  margin-top: 1rem;
  padding: 1rem;
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: var(--border-radius);
}

.add-child-form h4 {
  margin-top: 0;
  margin-bottom: 1rem;
  font-size: 1rem;
  color: var(--text-color);
}

.no-children {
  text-align: center;
  color: var(--text-secondary-color);
  font-style: italic;
  padding: 1rem;
}
