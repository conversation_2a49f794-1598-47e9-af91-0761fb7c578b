/* BeneficiaryForm Styles */
.beneficiary-form-container {
  width: 100%;
  padding: 1.5rem;
  background-color: var(--card-bg);
  border-radius: 8px;
  box-shadow: 0 2px 8px var(--shadow-color);
  margin-bottom: 2rem;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group.full-width {
  grid-column: span 2;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: bold;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--bg-color);
  color: var(--text-color);
  transition: border-color 0.3s, box-shadow 0.3s;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
  outline: none;
}

.form-group input.error,
.form-group select.error,
.form-group textarea.error {
  border-color: var(--error-color);
}

.error-text {
  display: block;
  color: var(--error-color);
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1.5rem;
}

.form-actions button {
  min-width: 120px;
}

/* Responsive styles */
@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
  }

  .form-group.full-width {
    grid-column: span 1;
  }

  .form-actions {
    flex-direction: column;
  }

  .form-actions button {
    width: 100%;
    margin-bottom: 0.5rem;
  }
}

/* Highlight field for focus */
.highlight-field {
  animation: pulse 2s;
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.3) !important;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(52, 152, 219, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(52, 152, 219, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(52, 152, 219, 0);
  }
}

/* Image upload container highlight */
#spouseIdImage-container.highlight-field .upload-btn,
#wifeIdImage-container.highlight-field .upload-btn,
#profileImage-container.highlight-field .upload-btn,
#spouseIdImage-container.highlight-field .image-preview,
#wifeIdImage-container.highlight-field .image-preview,
#profileImage-container.highlight-field .image-preview {
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.3) !important;
}

/* Form help text */
.form-help-text {
  display: block;
  color: var(--text-secondary-color);
  font-size: 0.8rem;
  margin-top: 0.5rem;
  font-style: italic;
}

/* Form section styling */
.form-section {
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--border-color);
}

.form-section h3 {
  margin-bottom: 1rem;
  color: var(--text-color);
  font-size: 1.1rem;
}

.form-row {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
}

@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
  }
}

