.about-daawa-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-md);
}

.page-header {
  margin-bottom: var(--spacing-lg);
  text-align: center;
}

.page-header h1 {
  color: var(--primary-color);
  margin-bottom: var(--spacing-sm);
}

.lead-text {
  font-size: 1.2rem;
  line-height: 1.6;
  color: var(--text-color);
  margin-bottom: var(--spacing-md);
}

/* Tabs Styling */
.tabs-container {
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  box-shadow: 0 2px 8px var(--shadow-color);
  overflow: hidden;
}

.tabs {
  display: flex;
  background-color: var(--bg-color);
  border-bottom: 1px solid var(--border-color);
}

.tab-btn {
  padding: var(--spacing-md) var(--spacing-lg);
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  color: var(--text-color);
  transition: all 0.3s ease;
  flex: 1;
  text-align: center;
}

.tab-btn:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.tab-btn.active {
  color: var(--primary-color);
  border-bottom: 2px solid var(--primary-color);
  background-color: rgba(52, 152, 219, 0.05);
}

.tab-content {
  padding: var(--spacing-lg);
}

/* Stats Styling */
.stats-container {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  gap: var(--spacing-md);
  margin: var(--spacing-lg) 0;
}

.stat-card {
  text-align: center;
  padding: var(--spacing-md);
  background-color: rgba(52, 152, 219, 0.05);
  border-radius: var(--border-radius);
  min-width: 150px;
  flex: 1;
}

.stat-icon {
  font-size: 2rem;
  color: var(--primary-color);
  margin-bottom: var(--spacing-sm);
}

.stat-number {
  font-size: 1.8rem;
  font-weight: bold;
  color: var(--text-color);
  margin-bottom: var(--spacing-xs);
}

.stat-label {
  color: var(--text-secondary-color);
  font-size: 0.9rem;
}

/* Mission & Vision Styling */
.mission-vision {
  display: flex;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  flex-wrap: wrap;
}

.mission-card,
.vision-card {
  flex: 1;
  min-width: 300px;
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  box-shadow: 0 2px 4px var(--shadow-color);
}

.mission-card {
  background-color: rgba(52, 152, 219, 0.05);
  border-right: 4px solid var(--primary-color);
}

.vision-card {
  background-color: rgba(46, 204, 113, 0.05);
  border-right: 4px solid var(--secondary-color);
}

.mission-card h3,
.vision-card h3 {
  margin-top: 0;
  margin-bottom: var(--spacing-sm);
  color: var(--text-color);
}

.values {
  margin-top: var(--spacing-lg);
}

.values-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
  padding: 0;
  list-style: none;
}

.values-list li {
  background-color: rgba(0, 0, 0, 0.05);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: 20px;
  font-size: 0.9rem;
}

/* Activities Styling */
.activities-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: var(--spacing-md);
  margin-top: var(--spacing-md);
}

.activity-card {
  padding: var(--spacing-md);
  background-color: var(--bg-color);
  border-radius: var(--border-radius);
  box-shadow: 0 2px 4px var(--shadow-color);
  transition: transform 0.3s ease;
}

.activity-card:hover {
  transform: translateY(-5px);
}

.activity-icon {
  font-size: 2rem;
  color: var(--primary-color);
  margin-bottom: var(--spacing-sm);
  text-align: center;
}

.activity-card h3 {
  margin-top: 0;
  margin-bottom: var(--spacing-sm);
  color: var(--text-color);
  text-align: center;
}

.activity-card ul {
  padding-right: var(--spacing-md);
  margin: 0;
}

.activity-card li {
  margin-bottom: var(--spacing-xs);
}

/* Contact Styling */
.contact-info {
  display: flex;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  flex-wrap: wrap;
}

.contact-card {
  flex: 1;
  min-width: 300px;
  padding: var(--spacing-md);
  background-color: var(--bg-color);
  border-radius: var(--border-radius);
  box-shadow: 0 2px 4px var(--shadow-color);
}

.contact-card h3 {
  margin-top: 0;
  margin-bottom: var(--spacing-sm);
  color: var(--text-color);
}

.social-media {
  margin-top: var(--spacing-lg);
}

.social-icons {
  display: flex;
  gap: var(--spacing-md);
  margin-top: var(--spacing-sm);
  flex-wrap: wrap;
}

.social-icon {
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--primary-color);
  color: white;
  border-radius: var(--border-radius);
  text-decoration: none;
  transition: background-color 0.3s ease;
}

.social-icon:hover {
  background-color: var(--secondary-color);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .tabs {
    flex-direction: column;
  }
  
  .tab-btn {
    border-bottom: 1px solid var(--border-color);
  }
  
  .tab-btn.active {
    border-bottom: 1px solid var(--border-color);
    border-right: 4px solid var(--primary-color);
  }
  
  .mission-vision,
  .contact-info {
    flex-direction: column;
  }
}
