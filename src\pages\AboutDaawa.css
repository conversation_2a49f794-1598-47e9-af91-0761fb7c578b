.about-daawa-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-md);
}

.page-header {
  margin-bottom: var(--spacing-lg);
  text-align: center;
}

.page-header h1 {
  color: var(--primary-color);
  margin-bottom: var(--spacing-sm);
}

.lead-text {
  font-size: 1.2rem;
  line-height: 1.6;
  color: var(--text-color);
  margin-bottom: var(--spacing-md);
}

/* Tabs Styling */
.tabs-container {
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  box-shadow: 0 2px 8px var(--shadow-color);
  overflow: hidden;
}

.tabs {
  display: flex;
  background-color: var(--bg-color);
  border-bottom: 1px solid var(--border-color);
  position: relative;
}

.tab-btn {
  padding: var(--spacing-md) var(--spacing-lg);
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  color: var(--text-color);
  transition: all 0.3s ease;
  flex: 1;
  text-align: center;
}

.tab-btn:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.tab-btn.active {
  color: var(--primary-color);
  border-bottom: 2px solid var(--primary-color);
  background-color: rgba(52, 152, 219, 0.05);
}

.tab-content {
  padding: var(--spacing-lg);
}

/* Stats Styling */
.stats-container {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  gap: var(--spacing-md);
  margin: var(--spacing-lg) 0;
}

.stat-card {
  text-align: center;
  padding: var(--spacing-md);
  background-color: rgba(52, 152, 219, 0.05);
  border-radius: var(--border-radius);
  min-width: 150px;
  flex: 1;
}

.stat-icon {
  font-size: 2rem;
  color: var(--primary-color);
  margin-bottom: var(--spacing-sm);
}

.stat-number {
  font-size: 1.8rem;
  font-weight: bold;
  color: var(--text-color);
  margin-bottom: var(--spacing-xs);
}

.stat-label {
  color: var(--text-secondary-color);
  font-size: 0.9rem;
}

/* Mission & Vision Styling */
.mission-vision {
  display: flex;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  flex-wrap: wrap;
}

.mission-card,
.vision-card {
  flex: 1;
  min-width: 300px;
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  box-shadow: 0 2px 4px var(--shadow-color);
}

.mission-card {
  background-color: rgba(52, 152, 219, 0.05);
  border-right: 4px solid var(--primary-color);
}

.vision-card {
  background-color: rgba(46, 204, 113, 0.05);
  border-right: 4px solid var(--secondary-color);
}

.mission-card h3,
.vision-card h3 {
  margin-top: 0;
  margin-bottom: var(--spacing-sm);
  color: var(--text-color);
}

.values {
  margin-top: var(--spacing-lg);
}

.values-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
  padding: 0;
  list-style: none;
}

.values-list li {
  background-color: rgba(0, 0, 0, 0.05);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: 20px;
  font-size: 0.9rem;
}

/* Activities Styling */
.activities-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: var(--spacing-md);
  margin-top: var(--spacing-md);
}

.activity-card {
  padding: var(--spacing-md);
  background-color: var(--bg-color);
  border-radius: var(--border-radius);
  box-shadow: 0 2px 4px var(--shadow-color);
  transition: transform 0.3s ease;
}

.activity-card:hover {
  transform: translateY(-5px);
}

.activity-icon {
  font-size: 2rem;
  color: var(--primary-color);
  margin-bottom: var(--spacing-sm);
  text-align: center;
}

.activity-card h3 {
  margin-top: 0;
  margin-bottom: var(--spacing-sm);
  color: var(--text-color);
  text-align: center;
}

.activity-card ul {
  padding-right: var(--spacing-md);
  margin: 0;
}

.activity-card li {
  margin-bottom: var(--spacing-xs);
}

/* Contact Styling */
.contact-info {
  display: flex;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  flex-wrap: wrap;
}

.contact-card {
  flex: 1;
  min-width: 300px;
  padding: var(--spacing-md);
  background-color: var(--bg-color);
  border-radius: var(--border-radius);
  box-shadow: 0 2px 4px var(--shadow-color);
}

.contact-card h3 {
  margin-top: 0;
  margin-bottom: var(--spacing-sm);
  color: var(--text-color);
}

.social-media {
  margin-top: var(--spacing-lg);
}

.social-icons {
  display: flex;
  gap: var(--spacing-md);
  margin-top: var(--spacing-sm);
  flex-wrap: wrap;
}

.social-icon {
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--primary-color);
  color: white;
  border-radius: var(--border-radius);
  text-decoration: none;
  transition: background-color 0.3s ease;
}

.social-icon:hover {
  background-color: var(--secondary-color);
}

/* Enhanced Tab Styling */
.tab-btn {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.tab-icon {
  font-size: 1.1rem;
}

.tab-indicator {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background-color: var(--primary-color);
}

/* Enhanced Statistics Cards */
.stat-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.stat-description {
  font-size: 0.85rem;
  color: var(--text-secondary-color);
  margin-top: 0.25rem;
}

/* Enhanced Mission and Vision Cards */
.card-icon {
  font-size: 2rem;
  color: var(--primary-color);
  margin-bottom: 1rem;
  text-align: center;
}

/* Enhanced Values Section */
.values-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.values-icon {
  font-size: 1.5rem;
  color: var(--primary-color);
}

.values-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.value-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background-color: var(--bg-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
  cursor: pointer;
}

.value-icon {
  font-size: 1.2rem;
  color: var(--primary-color);
}

/* Enhanced Activity Cards */
.activity-card {
  position: relative;
  overflow: hidden;
  cursor: pointer;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.activity-card:hover {
  border-color: var(--activity-color, var(--primary-color));
}

.activity-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  background: linear-gradient(135deg, var(--activity-color, var(--primary-color))20, transparent);
}

.activity-header h3 {
  margin: 0;
  flex-grow: 1;
  text-align: center;
}

.expand-icon {
  font-size: 1.2rem;
  color: var(--activity-color, var(--primary-color));
  cursor: pointer;
}

.activity-content {
  overflow: hidden;
  padding: 0 1rem;
}

.activity-content ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.activity-content li {
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--border-color);
  position: relative;
  padding-left: 1.5rem;
}

.activity-content li:before {
  content: "✓";
  position: absolute;
  left: 0;
  color: var(--activity-color, var(--primary-color));
  font-weight: bold;
}

.activity-stats {
  display: flex;
  justify-content: space-around;
  padding: 1rem;
  background-color: rgba(0, 0, 0, 0.02);
  border-top: 1px solid var(--border-color);
}

.activity-stats .stat {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
  color: var(--text-secondary-color);
}

/* Enhanced Contact Section */
.contact-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.contact-icon {
  font-size: 1.5rem;
  color: var(--primary-color);
}

.contact-details {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem;
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 4px;
}

.contact-item svg {
  color: var(--primary-color);
  font-size: 1.1rem;
}

.working-hours {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.hour-item {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem;
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 4px;
}

.hour-item.weekend {
  background-color: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
}

.social-icon {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background-color: var(--bg-color);
  color: var(--text-color);
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  text-decoration: none;
  transition: all 0.3s ease;
}

.social-icon:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Contact Form */
.contact-form-section {
  margin-top: 2rem;
  padding: 2rem;
  background-color: var(--bg-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.contact-form input,
.contact-form textarea {
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--card-bg);
  color: var(--text-color);
  transition: border-color 0.3s ease;
}

.contact-form input:focus,
.contact-form textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.submit-btn {
  padding: 0.75rem 2rem;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  align-self: flex-start;
}

.submit-btn:hover {
  background-color: var(--secondary-color);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .tabs {
    flex-direction: column;
  }

  .tab-btn {
    border-bottom: 1px solid var(--border-color);
  }

  .tab-btn.active {
    border-bottom: 1px solid var(--border-color);
    border-right: 4px solid var(--primary-color);
  }

  .mission-vision,
  .contact-info {
    flex-direction: column;
  }

  .values-grid {
    grid-template-columns: 1fr;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .social-icons {
    justify-content: center;
  }

  .activities-grid {
    grid-template-columns: 1fr;
  }
}
