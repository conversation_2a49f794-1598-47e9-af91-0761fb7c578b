/* Floating Action Button Styles */
.fab-button {
  position: fixed;
  bottom: 2rem;
  left: 2rem;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: var(--primary-color);
  color: white;
  border: none;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1.2rem;
  cursor: pointer;
  z-index: 1000;
  transition: all 0.3s ease;
}

.fab-button:hover {
  background-color: #2980b9;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
  transform: translateY(-3px);
}

.fab-button.open {
  transform: rotate(45deg);
  background-color: var(--error-color);
}

/* FAB Menu */
.fab-menu {
  position: fixed;
  bottom: 5rem;
  left: 2rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  z-index: 999;
}

.fab-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1.5rem;
  background-color: white;
  color: var(--text-color);
  border: none;
  border-radius: 30px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.fab-item span {
  margin-right: 0.5rem;
}

.fab-item:hover {
  background-color: var(--bg-color);
  transform: translateX(-5px);
}

/* Backdrop */
.fab-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 998;
}

/* Top-up Form */
.topup-form-container {
  position: fixed;
  bottom: 5vh;
  left: 5vw;
  width: 90vw;
  max-width: 90vw;
  max-height: 80vh;
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  z-index: 1001;
  overflow: auto; /* تمكين التمرير إذا كان المحتوى كبيرًا جدًا */
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.topup-form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2vh 3vw;
  background-color: var(--primary-color);
  color: white;
  position: sticky;
  top: 0;
  z-index: 1002;
}

.topup-form-header h3 {
  margin: 0;
  font-size: 1.2rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 30px;
  min-height: 30px;
  width: 6vw;
  height: 6vw;
  max-width: 40px;
  max-height: 40px;
  border-radius: 50%;
  transition: background-color 0.3s ease;
  margin-right: -1vw;
}

.close-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.topup-form-container form {
  padding: 3vh 4vw;
  flex: 1;
  overflow: auto;
}

.form-group {
  margin-bottom: 3vh;
  width: 100%;
}

.form-group label {
  display: block;
  margin-bottom: 1vh;
  font-weight: 500;
  color: var(--text-color);
  font-size: 0.95rem;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 1.5vh 2vw;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background-color: var(--bg-color);
  color: var(--text-color);
  font-size: 0.9rem;
}

.form-group textarea {
  min-height: 15vh;
  max-height: 25vh;
  resize: vertical;
}

/* Payment Methods */
.payment-methods {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  width: 100%;
  max-width: 100%;
  overflow: hidden;
  box-sizing: border-box;
}

/* إخفاء عنصر select مع الحفاظ على إمكانية الوصول */
.visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.payment-method {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: calc(25% - 0.5rem);
  padding: 2vh 2vw;
  background-color: var(--bg-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
}

.payment-method svg {
  font-size: 1.5rem;
  margin-bottom: 1vh;
  color: var(--text-secondary-color);
}

.payment-method span {
  font-size: 0.8rem;
  color: var(--text-secondary-color);
  white-space: nowrap;
}

.payment-method.active {
  background-color: rgba(52, 152, 219, 0.1);
  border-color: var(--primary-color);
  transform: scale(1.05);
}

.payment-method.active svg,
.payment-method.active span {
  color: var(--primary-color);
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 4vh;
  width: 100%;
}

.form-actions button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1.5vh 4vw;
  font-size: 0.95rem;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .topup-form-container {
    width: 90vw;
    max-width: 90vw;
    bottom: 10vh;
    left: 5vw;
    transform: none;
    max-height: 80vh;
  }

  .fab-button {
    bottom: 1.5rem;
    left: 1.5rem;
    width: 50px;
    height: 50px;
    z-index: 1100; /* زيادة z-index للتأكد من ظهور الزر فوق العناصر الأخرى */
  }

  .fab-menu {
    bottom: 4.5rem;
    left: 1.5rem;
  }

  .form-group {
    margin-bottom: 2vh;
  }

  .form-actions {
    margin-top: 3vh;
    margin-bottom: 2vh;
  }

  .form-actions button {
    width: 100%;
    justify-content: center;
    padding: 1.5vh 0;
  }
}

/* تعديلات إضافية للشاشات الصغيرة جدًا */
@media (max-width: 480px) {
  .topup-form-container {
    width: 90vw;
    max-width: 90vw;
    bottom: 5vh;
    left: 5vw;
    max-height: 90vh;
  }

  .fab-button {
    bottom: 1rem;
    left: 1rem;
    width: 45px;
    height: 45px;
    font-size: 1rem;
  }

  .fab-menu {
    bottom: 4rem;
    left: 1rem;
  }

  .fab-item {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }

  .payment-method {
    width: calc(50% - 1vw); /* جعل طرق الدفع في صفين بدلاً من أربعة */
    padding: 1.5vh 2vw;
  }

  .payment-method svg {
    font-size: 6vw;
  }

  .payment-method span {
    font-size: 0.75rem;
  }

  .topup-form-header h3 {
    font-size: 1rem;
  }
}

/* تأكيد ظهور الزر في جميع الحالات */
.fab-button,
button.fab-button,
.fn-button,
button.fn-button {
  visibility: visible !important;
  opacity: 1 !important;
  display: flex !important;
  position: fixed !important;
  z-index: 9999 !important;
}

/* تعريف فئة fn-button كبديل لـ fab-button للتوافق */
.fn-button {
  position: fixed;
  bottom: 2rem;
  left: 2rem;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: var(--primary-color);
  color: white;
  border: none;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1.2rem;
  cursor: pointer;
  z-index: 1000;
  transition: all 0.3s ease;
}
