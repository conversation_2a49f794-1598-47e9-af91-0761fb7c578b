.volunteer-registration-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  padding: 2rem;
}

/* Header Styles */
.registration-header {
  text-align: center;
  margin-bottom: 3rem;
}

.header-content {
  background: white;
  padding: 3rem 2rem;
  border-radius: 20px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
}

.header-icon {
  font-size: 4rem;
  color: #3498db;
  margin-bottom: 1rem;
}

.header-content h1 {
  font-size: 2.5rem;
  color: #2c3e50;
  margin: 0 0 1rem 0;
  font-weight: 700;
}

.header-content p {
  font-size: 1.2rem;
  color: #7f8c8d;
  margin: 0;
  line-height: 1.6;
}

/* Form Container */
.form-container {
  background: white;
  border-radius: 20px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  padding: 3rem;
}

.registration-form {
  max-width: 1000px;
  margin: 0 auto;
}

/* Form Sections */
.form-section {
  margin-bottom: 3rem;
  padding-bottom: 2rem;
  border-bottom: 2px solid #f8f9fa;
}

.form-section:last-of-type {
  border-bottom: none;
  margin-bottom: 2rem;
}

.form-section h2 {
  display: flex;
  align-items: center;
  gap: 1rem;
  color: #2c3e50;
  font-size: 1.8rem;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #3498db;
}

.form-section h2 svg {
  color: #3498db;
  font-size: 1.5rem;
}

/* Form Grid */
.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  font-weight: 600;
  color: #2c3e50;
  font-size: 1rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 1rem;
  border: 2px solid #e1e8ed;
  border-radius: 10px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: #f8f9fa;
  font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #3498db;
  background: white;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.form-group input.error,
.form-group select.error {
  border-color: #e74c3c;
  background: #fdf2f2;
}

.form-group input.error:focus,
.form-group select.error:focus {
  border-color: #e74c3c;
  box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 100px;
}

/* Error Messages */
.error-message {
  color: #e74c3c;
  font-size: 0.9rem;
  font-weight: 500;
  margin-top: 0.25rem;
}

/* Form Actions */
.form-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 2px solid #f8f9fa;
}

.btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  border: none;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  min-width: 150px;
  justify-content: center;
}

.btn-primary {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.btn.loading {
  position: relative;
}

/* Spinner */
.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Success/Error States */
.form-group.success input,
.form-group.success select {
  border-color: #27ae60;
  background: #f0f9f4;
}

.form-group.success input:focus,
.form-group.success select:focus {
  border-color: #27ae60;
  box-shadow: 0 0 0 3px rgba(39, 174, 96, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
  .volunteer-registration-container {
    padding: 1rem;
  }
  
  .header-content {
    padding: 2rem 1rem;
  }
  
  .header-content h1 {
    font-size: 2rem;
  }
  
  .form-container {
    padding: 2rem 1rem;
  }
  
  .form-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .form-section h2 {
    font-size: 1.5rem;
  }
  
  .form-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .btn {
    width: 100%;
    max-width: 250px;
  }
}

@media (max-width: 480px) {
  .header-content {
    padding: 1.5rem 1rem;
  }
  
  .header-content h1 {
    font-size: 1.8rem;
  }
  
  .header-content p {
    font-size: 1rem;
  }
  
  .form-container {
    padding: 1.5rem 1rem;
  }
  
  .form-section {
    margin-bottom: 2rem;
  }
  
  .form-section h2 {
    font-size: 1.3rem;
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .volunteer-registration-container {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  }
  
  .header-content,
  .form-container {
    background: #34495e;
    color: #ecf0f1;
  }
  
  .form-section h2,
  .header-content h1 {
    color: #ecf0f1;
  }
  
  .form-group label {
    color: #ecf0f1;
  }
  
  .form-group input,
  .form-group select,
  .form-group textarea {
    background: #2c3e50;
    border-color: #4a5f7a;
    color: #ecf0f1;
  }
  
  .form-group input:focus,
  .form-group select:focus,
  .form-group textarea:focus {
    background: #34495e;
  }
  
  .form-section {
    border-bottom-color: #4a5f7a;
  }
  
  .form-actions {
    border-top-color: #4a5f7a;
  }
}

/* Print Styles */
@media print {
  .volunteer-registration-container {
    background: white;
    padding: 1rem;
  }
  
  .header-content,
  .form-container {
    box-shadow: none;
    border: 1px solid #ddd;
  }
  
  .form-actions {
    display: none;
  }
}
