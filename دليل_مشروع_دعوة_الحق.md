# دليل مشروع دعوة الحق الشامل

## مقدمة

مشروع "دعوة الحق" هو تطبيق ويب متكامل لإدارة المبادرات الخيرية والمستفيدين والمعاملات المالية. تم تطوير المشروع باستخدام React وVite مع استخدام Redux لإدارة حالة التطبيق. يتميز التطبيق بواجهة مستخدم عربية سهلة الاستخدام وتدعم الأجهزة المختلفة.

## هيكل المشروع

```
elhaqPro/
├── public/                  # الملفات العامة
├── src/                     # كود المصدر
│   ├── app/                 # إعدادات التطبيق
│   ├── assets/              # الصور والموارد
│   ├── components/          # المكونات القابلة لإعادة الاستخدام
│   ├── features/            # ميزات التطبيق (مع Redux slices)
│   ├── pages/               # صفحات التطبيق
│   ├── routes/              # تكوين المسارات
│   ├── store/               # متجر Redux
│   ├── utils/               # أدوات مساعدة
│   ├── App.jsx              # المكون الرئيسي للتطبيق
│   ├── App.css              # أنماط CSS الرئيسية
│   ├── main.jsx             # نقطة الدخول للتطبيق
│   └── index.css            # أنماط CSS العامة
├── .gitignore               # ملفات مستثناة من Git
├── firebase.json            # إعدادات Firebase
├── .firebaserc              # إعدادات مشروع Firebase
├── package.json             # تبعيات المشروع
├── vite.config.js           # إعدادات Vite
└── README.md                # توثيق المشروع
```

## التقنيات المستخدمة

- **React**: مكتبة JavaScript لبناء واجهات المستخدم
- **Vite**: أداة بناء سريعة للتطبيقات الحديثة
- **Redux**: مكتبة لإدارة حالة التطبيق
- **Redux Toolkit**: مجموعة أدوات لتبسيط استخدام Redux
- **React Router**: للتنقل بين صفحات التطبيق
- **Framer Motion**: لإضافة حركات وانتقالات سلسة
- **Firebase**: للاستضافة وخدمات الباك إند
- **React Icons**: لاستخدام الأيقونات في التطبيق

## المكونات الرئيسية

### 1. نظام المستفيدين

يتيح النظام إدارة المستفيدين من خلال:
- إضافة مستفيدين جدد
- عرض بيانات المستفيدين
- تعديل بيانات المستفيدين
- إدارة بيانات أبناء المستفيدين
- إضافة ملاحظات للمستفيدين

### 2. نظام المبادرات

يتيح النظام إدارة المبادرات الخيرية من خلال:
- إنشاء مبادرات جديدة
- ربط المستفيدين بالمبادرات
- متابعة حالة المبادرات
- عرض تقارير عن المبادرات

### 3. النظام المالي

يتيح النظام إدارة المعاملات المالية من خلال:
- تسجيل الإيرادات والمصروفات
- ربط المصروفات بالمستفيدين
- إضافة عمليات "توب أب" سريعة
- عرض تقارير مالية وإحصائيات

### 4. نظام المستخدمين

يتيح النظام إدارة المستخدمين من خلال:
- تسجيل الدخول
- إنشاء حسابات جديدة
- إدارة صلاحيات المستخدمين

### 5. نظام الإعدادات

يتيح النظام تخصيص إعدادات التطبيق مثل:
- تغيير اللغة
- تغيير المظهر (الوضع الليلي/النهاري)
- تخصيص إعدادات العرض

## شرح المسميات البرمجية

### المكونات (Components)

المكونات هي وحدات قابلة لإعادة الاستخدام في React، وتستخدم لبناء واجهة المستخدم. أهم المكونات في المشروع:

#### BeneficiaryForm
مكون لإضافة وتعديل بيانات المستفيدين.

```jsx
// مثال على استخدام المكون
<BeneficiaryForm
  beneficiary={selectedBeneficiary}
  onSubmit={handleSubmitBeneficiary}
  isEditing={true}
/>
```

#### ChildrenManager
مكون لإدارة بيانات أبناء المستفيدين.

```jsx
// مثال على استخدام المكون
<ChildrenManager
  children={beneficiary.children}
  onChange={handleChildrenChange}
/>
```

#### FloatingActionButton
زر عائم يظهر في جميع صفحات التطبيق ويتيح إضافة "توب أب" سريع.

```jsx
// مثال على استخدام المكون
<FloatingActionButton />
```

#### InitiativeForm
مكون لإضافة وتعديل المبادرات.

```jsx
// مثال على استخدام المكون
<InitiativeForm
  initiative={selectedInitiative}
  onComplete={handleInitiativeSubmit}
  isEditing={false}
/>
```

### الصفحات (Pages)

الصفحات هي مكونات React تمثل صفحات كاملة في التطبيق. أهم الصفحات في المشروع:

#### Home
الصفحة الرئيسية للتطبيق، تعرض ملخصًا للإحصائيات والمعلومات الهامة.

#### Finance
صفحة إدارة المعاملات المالية، تتيح إضافة وعرض الإيرادات والمصروفات.

#### Initiatives
صفحة إدارة المبادرات، تتيح إنشاء وعرض المبادرات الخيرية.

#### AboutDaawa
صفحة تعريفية عن "دعوة الحق" وأهدافها ورسالتها.

#### Settings
صفحة الإعدادات، تتيح تخصيص إعدادات التطبيق.

### Redux Slices

Redux Slices هي وحدات منطقية في Redux Toolkit تجمع بين reducers وactions. أهم الـ slices في المشروع:

#### authSlice
يدير حالة المصادقة في التطبيق (تسجيل الدخول، إنشاء حساب، تسجيل الخروج).

#### beneficiariesSlice
يدير بيانات المستفيدين (إضافة، تعديل، حذف، استرجاع).

#### financeSlice
يدير المعاملات المالية (إيرادات، مصروفات، إحصائيات).

#### initiativesSlice
يدير المبادرات الخيرية (إنشاء، تعديل، حذف، استرجاع).

#### notificationsSlice
يدير الإشعارات في التطبيق.

#### settingsSlice
يدير إعدادات التطبيق.

#### themeSlice
يدير مظهر التطبيق (الوضع الليلي/النهاري).

## أسئلة وإجابات حول المشروع

### س1: ما هو الهدف من مشروع "دعوة الحق"؟
ج1: مشروع "دعوة الحق" يهدف إلى إدارة المبادرات الخيرية والمستفيدين والمعاملات المالية بطريقة منظمة وسهلة الاستخدام، مما يساعد في تحسين كفاءة العمل الخيري وزيادة الشفافية.

### س2: ما هي التقنيات الرئيسية المستخدمة في المشروع؟
ج2: التقنيات الرئيسية المستخدمة هي React، Vite، Redux Toolkit، React Router، Framer Motion، وFirebase.

### س3: كيف يتم إدارة حالة التطبيق؟
ج3: يتم إدارة حالة التطبيق باستخدام Redux Toolkit، حيث يتم تقسيم الحالة إلى "slices" منطقية مثل المستفيدين والمبادرات والمعاملات المالية.

### س4: كيف يتم تخزين البيانات في التطبيق؟
ج4: يتم تخزين البيانات محليًا باستخدام localStorage للاحتفاظ بالبيانات بين جلسات المستخدم، مع إمكانية التكامل مع Firebase لتخزين البيانات على السحابة.

### س5: كيف يتم التعامل مع واجهة المستخدم المتجاوبة؟
ج5: يتم استخدام CSS المتجاوب مع وحدات قياس نسبية (مثل vw, vh) وmedia queries لضمان عرض التطبيق بشكل صحيح على مختلف أحجام الشاشات.

### س6: ما هي آلية عمل نظام الإشعارات في التطبيق؟
ج6: يستخدم التطبيق نظام إشعارات مبني على Redux، حيث يتم إضافة الإشعارات إلى قائمة في Redux store، ويتم عرضها باستخدام مكون ToastNotifications الذي يعرض الإشعارات بشكل مؤقت ثم يزيلها تلقائيًا.

### س7: كيف يتم التعامل مع المصادقة في التطبيق؟
ج7: يستخدم التطبيق نظام مصادقة مبني على Redux، حيث يتم تخزين حالة المستخدم في Redux store. يمكن تكامل النظام مع Firebase Authentication لتوفير مصادقة آمنة.

### س8: ما هي ميزات النظام المالي في التطبيق؟
ج8: يوفر النظام المالي إمكانية تسجيل الإيرادات والمصروفات، وربط المصروفات بالمستفيدين، وإضافة عمليات "توب أب" سريعة، وعرض تقارير مالية وإحصائيات، وحساب المبالغ المخصصة لكل مستفيد.

### س9: كيف يتم تنظيم المسارات (Routes) في التطبيق؟
ج9: يتم تنظيم المسارات باستخدام React Router، حيث يتم تعريف المسارات في ملف routes/index.jsx، ويتم استخدام مكون ProtectedRoute لحماية المسارات التي تتطلب تسجيل الدخول.

### س10: ما هي آلية عمل زر التوب أب العائم؟
ج10: زر التوب أب العائم (FloatingActionButton) هو مكون يظهر في جميع صفحات التطبيق ويتيح للمستخدم إضافة معاملة مالية سريعة (توب أب) دون الحاجة للانتقال إلى صفحة المالية. يستخدم المكون Framer Motion لإضافة حركات سلسة.

## شرح تفصيلي للكود

### 1. مكون FloatingActionButton

```jsx
// src/components/FloatingActionButton.jsx
import { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { motion, AnimatePresence } from 'framer-motion';
import { FaPlus, FaMoneyBillWave, FaTimes } from 'react-icons/fa';
import { addTransaction, calculateStats } from '../features/finance/financeSlice';
import { addNotification } from '../features/notifications/notificationsSlice';
import './FloatingActionButton.css';

const FloatingActionButton = () => {
  const dispatch = useDispatch();
  const [isOpen, setIsOpen] = useState(false);
  const [showTopUpForm, setShowTopUpForm] = useState(false);
  const [topUpData, setTopUpData] = useState({
    amount: '',
    method: 'cash',
    description: '',
    date: new Date().toISOString().split('T')[0],
  });

  // Toggle the FAB menu
  const toggleMenu = () => {
    setIsOpen(!isOpen);
    if (showTopUpForm) {
      setShowTopUpForm(false);
    }
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();

    const transaction = {
      date: topUpData.date,
      amount: parseFloat(topUpData.amount),
      type: 'income',
      description: `توب أب: ${topUpData.description}`,
      beneficiaries: []
    };

    dispatch(addTransaction(transaction))
      .unwrap()
      .then(() => {
        dispatch(addNotification({
          type: 'success',
          message: 'تم إضافة التوب أب بنجاح'
        }));
        dispatch(calculateStats());
        setShowTopUpForm(false);
        setIsOpen(false);
      })
      .catch(error => {
        dispatch(addNotification({
          type: 'error',
          message: 'حدث خطأ أثناء إضافة التوب أب'
        }));
      });
  };

  return (
    <>
      {/* Backdrop */}
      <AnimatePresence>
        {(isOpen || showTopUpForm) && (
          <motion.div
            className="fab-backdrop"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => {
              setIsOpen(false);
              setShowTopUpForm(false);
            }}
          />
        )}
      </AnimatePresence>

      {/* Top-up Form */}
      <AnimatePresence>
        {showTopUpForm && (
          <motion.div
            className="topup-form-container"
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 50 }}
          >
            {/* Form content */}
          </motion.div>
        )}
      </AnimatePresence>

      {/* FAB Menu */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            className="fab-menu"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
          >
            <motion.button
              className="fab-item"
              onClick={() => setShowTopUpForm(true)}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <FaMoneyBillWave />
              <span>توب أب</span>
            </motion.button>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main FAB Button */}
      <motion.button
        className={`fab-button ${isOpen ? 'open' : ''}`}
        onClick={toggleMenu}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
      >
        {isOpen ? <FaTimes /> : <FaPlus />}
      </motion.button>
    </>
  );
};

export default FloatingActionButton;
```

**شرح المكون:**
- يستخدم `useState` لإدارة حالة الزر والنموذج
- يستخدم `useDispatch` للتفاعل مع Redux store
- يستخدم `AnimatePresence` و `motion` من Framer Motion لإضافة حركات سلسة
- يتكون من ثلاثة أجزاء رئيسية:
  1. الزر الرئيسي (Main FAB Button)
  2. قائمة الخيارات (FAB Menu)
  3. نموذج التوب أب (Top-up Form)
- عند النقر على الزر الرئيسي، تظهر قائمة الخيارات
- عند النقر على خيار "توب أب"، يظهر نموذج إضافة توب أب
- عند تقديم النموذج، يتم إضافة معاملة مالية جديدة وإظهار إشعار نجاح

### 2. صفحة Finance

```jsx
// src/pages/Finance.jsx (مقتطف)
import { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { motion, AnimatePresence } from 'framer-motion';
import {
  selectAllTransactions,
  selectFinanceStats,
  addTransaction,
  deleteTransaction,
  calculateStats
} from '../features/finance/financeSlice';
import { selectAllBeneficiaries } from '../features/beneficiaries/beneficiariesSlice';
import './Finance.css';

const Finance = () => {
  const dispatch = useDispatch();
  const transactions = useSelector(selectAllTransactions);
  const stats = useSelector(selectFinanceStats);
  const beneficiaries = useSelector(selectAllBeneficiaries);

  const [showAddForm, setShowAddForm] = useState(false);
  const [newTransaction, setNewTransaction] = useState({
    date: new Date().toISOString().split('T')[0],
    type: 'income',
    amount: '',
    description: '',
    beneficiaries: []
  });

  // إعادة حساب الإحصائيات عند تغيير المعاملات
  useEffect(() => {
    dispatch(calculateStats());
  }, [transactions, dispatch]);

  // إضافة معاملة جديدة
  const handleSubmit = (e) => {
    e.preventDefault();

    const transactionData = {
      ...newTransaction,
      amount: parseFloat(newTransaction.amount),
      beneficiaries: beneficiaries.filter(b =>
        selectedBeneficiaries.includes(b.id)
      )
    };

    dispatch(addTransaction(transactionData))
      .unwrap()
      .then(() => {
        setShowAddForm(false);
        resetForm();
      });
  };

  // حذف معاملة
  const handleDeleteTransaction = (id) => {
    if (window.confirm('هل أنت متأكد من حذف هذه المعاملة؟')) {
      dispatch(deleteTransaction(id));
    }
  };

  return (
    <motion.div
      className="finance-container"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      {/* محتوى الصفحة */}
    </motion.div>
  );
};

export default Finance;
```

**شرح الصفحة:**
- تستخدم `useSelector` لاسترجاع البيانات من Redux store
- تستخدم `useDispatch` لإرسال actions إلى Redux store
- تستخدم `useState` لإدارة حالة النماذج والبيانات المحلية
- تستخدم `useEffect` لإعادة حساب الإحصائيات عند تغيير المعاملات
- توفر وظائف لإضافة وحذف المعاملات المالية
- تستخدم Framer Motion لإضافة حركات سلسة للعناصر

### 3. Redux Slice للمعاملات المالية

```javascript
// src/features/finance/financeSlice.js (مقتطف)
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';

// استرجاع المعاملات من التخزين المحلي
const getTransactionsFromStorage = () => {
  const savedTransactions = localStorage.getItem('transactions');
  return savedTransactions ? JSON.parse(savedTransactions) : [];
};

// حفظ المعاملات في التخزين المحلي
const saveTransactionsToStorage = (transactions) => {
  localStorage.setItem('transactions', JSON.stringify(transactions));
};

// حساب الإحصائيات
const calculateInitialStats = (transactions) => {
  let totalIncome = 0;
  let totalExpenses = 0;

  transactions.forEach(transaction => {
    const amount = parseFloat(transaction.amount);
    if (transaction.type === 'income') {
      totalIncome += amount;
    } else {
      totalExpenses += amount;
    }
  });

  return {
    totalIncome,
    totalExpenses,
    balance: totalIncome - totalExpenses
  };
};

// الحالة الأولية
const initialState = {
  transactions: getTransactionsFromStorage(),
  isLoading: false,
  error: null,
  stats: calculateInitialStats(getTransactionsFromStorage())
};

// إضافة معاملة جديدة
export const addTransaction = createAsyncThunk(
  'finance/addTransaction',
  async (transaction, { rejectWithValue }) => {
    try {
      const newTransaction = {
        ...transaction,
        id: Date.now().toString(),
        createdAt: new Date().toISOString()
      };
      return newTransaction;
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

// حذف معاملة
export const deleteTransaction = createAsyncThunk(
  'finance/deleteTransaction',
  async (id, { rejectWithValue }) => {
    try {
      return id;
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

// تعريف الـ slice
const financeSlice = createSlice({
  name: 'finance',
  initialState,
  reducers: {
    calculateStats: (state) => {
      state.stats = calculateInitialStats(state.transactions);
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(addTransaction.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(addTransaction.fulfilled, (state, action) => {
        state.isLoading = false;
        state.transactions.push(action.payload);
        saveTransactionsToStorage(state.transactions);
        state.stats = calculateInitialStats(state.transactions);
      })
      .addCase(addTransaction.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      .addCase(deleteTransaction.fulfilled, (state, action) => {
        state.transactions = state.transactions.filter(
          transaction => transaction.id !== action.payload
        );
        saveTransactionsToStorage(state.transactions);
        state.stats = calculateInitialStats(state.transactions);
      });
  }
});

// تصدير الـ actions والـ selectors والـ reducer
export const { calculateStats } = financeSlice.actions;
export const selectAllTransactions = (state) => state.finance.transactions;
export const selectFinanceStats = (state) => state.finance.stats;
export const selectFinanceLoading = (state) => state.finance.isLoading;

export default financeSlice.reducer;
```

**شرح الـ Slice:**
- يستخدم `createSlice` من Redux Toolkit لإنشاء slice للمعاملات المالية
- يستخدم `createAsyncThunk` لإنشاء actions غير متزامنة
- يستخدم localStorage لتخزين المعاملات محليًا
- يوفر وظائف لإضافة وحذف المعاملات وحساب الإحصائيات
- يصدر selectors للوصول إلى البيانات من مكونات React

## كيفية استخدام المشروع

### 1. تثبيت التبعيات

```bash
npm install
```

### 2. تشغيل المشروع محليًا

```bash
npm run dev
```

### 3. بناء المشروع للإنتاج

```bash
npm run build
```

### 4. نشر المشروع على Firebase

```bash
firebase deploy
```

## شرح المصطلحات البرمجية المستخدمة

### مصطلحات React

| المصطلح | الشرح |
|---------|-------|
| **Component** | وحدة قابلة لإعادة الاستخدام في React، تمثل جزءًا من واجهة المستخدم. |
| **Props** | البيانات التي يتم تمريرها من المكون الأب إلى المكون الابن. |
| **State** | البيانات المحلية للمكون التي يمكن تغييرها وتؤثر على طريقة عرض المكون. |
| **Hooks** | وظائف خاصة في React تتيح استخدام ميزات React في المكونات الوظيفية. |
| **useEffect** | Hook يستخدم لتنفيذ آثار جانبية في المكونات الوظيفية. |
| **useState** | Hook يستخدم لإضافة حالة محلية إلى المكونات الوظيفية. |
| **useSelector** | Hook من Redux يستخدم لاسترجاع البيانات من Redux store. |
| **useDispatch** | Hook من Redux يستخدم للحصول على وظيفة dispatch لإرسال actions. |
| **JSX** | امتداد لـ JavaScript يتيح كتابة HTML داخل JavaScript. |

### مصطلحات Redux

| المصطلح | الشرح |
|---------|-------|
| **Store** | كائن يحتفظ بحالة التطبيق بالكامل. |
| **Action** | كائن يصف ما حدث في التطبيق. |
| **Reducer** | وظيفة تحدد كيفية تغيير حالة التطبيق استجابة للـ actions. |
| **Slice** | جزء من Redux store يتضمن reducer وactions مرتبطة بميزة معينة. |
| **Selector** | وظيفة تستخدم لاسترجاع جزء من حالة Redux. |
| **Thunk** | وظيفة تتيح تنفيذ عمليات غير متزامنة في Redux. |
| **Dispatch** | وظيفة تستخدم لإرسال actions إلى Redux store. |

### مصطلحات Framer Motion

| المصطلح | الشرح |
|---------|-------|
| **motion** | مكون أساسي في Framer Motion يضيف إمكانيات الحركة. |
| **AnimatePresence** | مكون يتيح حركات الخروج للعناصر التي تتم إزالتها من DOM. |
| **variants** | كائنات تحدد حالات الحركة المختلفة للعناصر. |
| **initial** | الحالة الأولية للعنصر عند ظهوره. |
| **animate** | الحالة التي ينتقل إليها العنصر بعد ظهوره. |
| **exit** | الحالة التي ينتقل إليها العنصر قبل إزالته. |
| **transition** | تحدد كيفية انتقال العنصر بين الحالات المختلفة. |

### مصطلحات Firebase

| المصطلح | الشرح |
|---------|-------|
| **Hosting** | خدمة استضافة مواقع الويب من Firebase. |
| **Authentication** | خدمة إدارة المستخدمين والمصادقة من Firebase. |
| **Firestore** | قاعدة بيانات NoSQL سحابية من Firebase. |
| **Storage** | خدمة تخزين الملفات من Firebase. |
| **Firebase CLI** | واجهة سطر الأوامر للتفاعل مع Firebase. |
| **firebase.json** | ملف تكوين يحدد إعدادات مشروع Firebase. |
| **.firebaserc** | ملف يحدد مشروع Firebase الافتراضي. |

## أفكار للتطوير المستقبلي

1. **تكامل كامل مع Firebase**:
   - استخدام Firestore لتخزين البيانات بدلاً من localStorage
   - استخدام Firebase Authentication لإدارة المستخدمين
   - استخدام Firebase Storage لتخزين الصور والملفات

2. **تحسينات واجهة المستخدم**:
   - إضافة رسوم بيانية متقدمة للإحصائيات
   - تحسين تجربة المستخدم على الأجهزة المحمولة
   - إضافة وضع عدم الاتصال (offline mode)

3. **ميزات جديدة**:
   - نظام تقارير متقدم
   - تكامل مع خدمات الدفع الإلكتروني
   - نظام إشعارات متقدم (إشعارات بريد إلكتروني، إشعارات دفع)
   - نظام مشاركة وتعاون للمستخدمين

4. **تحسينات تقنية**:
   - تحسين أداء التطبيق وتقليل حجم الحزم
   - إضافة اختبارات آلية (unit tests, integration tests)
   - تحسين SEO وإمكانية الوصول

## الخلاصة

مشروع "دعوة الحق" هو تطبيق ويب متكامل لإدارة المبادرات الخيرية والمستفيدين والمعاملات المالية. يتميز بواجهة مستخدم عربية سهلة الاستخدام وبنية برمجية منظمة تعتمد على React وRedux.

يوفر هذا الدليل شرحًا تفصيليًا للمشروع وكيفية استخدامه وتطويره، بالإضافة إلى شرح للمصطلحات البرمجية المستخدمة. يمكن استخدام هذا الدليل كمرجع للمطورين الجدد الذين ينضمون إلى المشروع أو للمستخدمين الذين يرغبون في فهم كيفية عمل التطبيق.

يمكن تطوير المشروع مستقبلاً بإضافة المزيد من الميزات وتحسين الأداء والتكامل مع خدمات أخرى لجعله أكثر فائدة وكفاءة في إدارة العمل الخيري.
