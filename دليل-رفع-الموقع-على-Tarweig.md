# 🚀 دليل رفع موقع دعوة الحق على استضافة Tarweig

## 📦 **الملفات الجاهزة:**
✅ **ملف مضغوط**: `elhaq-website-tarweig.zip` (6.4 MB)
✅ **جميع الملفات محسنة ومجهزة للرفع**
✅ **إعدادات الأمان والأداء مضافة**

---

## 🎯 **خطوات الرفع التفصيلية:**

### **المرحلة الأولى: الوصول للاستضافة**

#### **1️⃣ تسجيل الدخول:**
1. اذهب إلى: https://clients.tarweig.com/clientarea.php
2. أدخل بيانات حسابك (اسم المستخدم وكلمة المرور)
3. اضغط "تسجيل الدخول"

#### **2️⃣ اختيار الباقة:**
1. من القائمة الرئيسية، اختر **"خدماتي"** أو **"My Services"**
2. اختر **"الباقة الاقتصادية"**
3. اضغط **"إدارة"** أو **"Manage"**

#### **3️⃣ فتح cPanel:**
1. ابحث عن زر **"cPanel"** أو **"لوحة التحكم"**
2. اضغط عليه لفتح لوحة تحكم الاستضافة
3. ستفتح نافذة جديدة بلوحة التحكم

---

### **المرحلة الثانية: رفع الملفات**

#### **4️⃣ فتح مدير الملفات:**
1. في cPanel، ابحث عن **"File Manager"** أو **"مدير الملفات"**
2. اضغط عليه لفتح مدير الملفات
3. ستظهر لك شجرة المجلدات

#### **5️⃣ الانتقال لمجلد الموقع:**
1. اضغط على مجلد **"public_html"** (هذا مجلد الموقع الرئيسي)
2. إذا وجدت ملفات قديمة (مثل index.html أو default.html)، احذفها
3. تأكد أن المجلد فارغ أو يحتوي فقط على مجلدات النظام

#### **6️⃣ رفع الملف المضغوط:**
1. اضغط على زر **"Upload"** أو **"رفع"** في الأعلى
2. اضغط **"Select File"** أو **"اختيار ملف"**
3. اختر ملف `elhaq-website-tarweig.zip` من جهازك
4. انتظر حتى ينتهي الرفع (قد يستغرق 2-5 دقائق)
5. ستظهر رسالة "Upload Complete" عند الانتهاء

#### **7️⃣ استخراج الملفات:**
1. ارجع إلى File Manager
2. ستجد ملف `elhaq-website-tarweig.zip` في مجلد public_html
3. انقر بالزر الأيمن على الملف
4. اختر **"Extract"** أو **"استخراج"**
5. تأكد أن المسار هو `/public_html/`
6. اضغط **"Extract Files"**
7. انتظر حتى ينتهي الاستخراج

#### **8️⃣ تنظيف الملفات:**
1. بعد الاستخراج، احذف ملف `elhaq-website-tarweig.zip`
2. تأكد أن جميع الملفات موجودة في `public_html` مباشرة
3. يجب أن ترى: `index.html`, `.htaccess`, `assets/`, `images/`, إلخ

---

### **المرحلة الثالثة: الإعدادات**

#### **9️⃣ إعداد PHP:**
1. في cPanel، ابحث عن **"PHP Version"** أو **"إصدار PHP"**
2. اختر **PHP 8.0** أو أحدث
3. احفظ التغييرات

#### **🔟 تفعيل SSL:**
1. في cPanel، ابحث عن **"SSL/TLS"**
2. اضغط على **"Let's Encrypt SSL"**
3. اختر الدومين الخاص بك
4. اضغط **"Install"** أو **"تثبيت"**
5. انتظر حتى يتم التثبيت (2-5 دقائق)

#### **1️⃣1️⃣ تحديث الروابط:**
1. في File Manager، افتح ملف `sitemap.xml`
2. استبدل `https://yourdomain.com` بالدومين الفعلي لموقعك
3. احفظ الملف
4. افتح ملف `robots.txt` وقم بنفس التحديث

---

### **المرحلة الرابعة: الاختبار**

#### **1️⃣2️⃣ اختبار الموقع:**
1. افتح متصفح جديد
2. اذهب إلى رابط موقعك
3. تأكد من ظهور الصفحة الرئيسية بشكل صحيح

#### **1️⃣3️⃣ اختبار الصفحات:**
اختبر جميع هذه الروابط:
- [ ] الصفحة الرئيسية: `yourdomain.com/`
- [ ] عن دعوة الحق: `yourdomain.com/about`
- [ ] المتطوعون: `yourdomain.com/volunteers`
- [ ] المستفيدون: `yourdomain.com/beneficiaries`
- [ ] المبادرات: `yourdomain.com/initiatives`
- [ ] الماليات: `yourdomain.com/finance`
- [ ] التقارير: `yourdomain.com/reports`
- [ ] انضم كمتطوع: `yourdomain.com/join-volunteer`
- [ ] تسجيل متطوع: `yourdomain.com/volunteer-registration`

#### **1️⃣4️⃣ اختبار الوظائف:**
- [ ] تسجيل الدخول والخروج
- [ ] إضافة مستفيد جديد
- [ ] إضافة متطوع جديد
- [ ] البحث في المستفيدين
- [ ] معرض الصور
- [ ] النماذج التفاعلية

---

## 🔧 **حل المشاكل الشائعة:**

### **❌ مشكلة: صفحة 404 عند فتح الروابط**
**السبب**: ملف .htaccess غير مرفوع أو معطل
**الحل:**
1. تأكد من وجود ملف `.htaccess` في public_html
2. تأكد أن mod_rewrite مفعل (اتصل بالدعم الفني إذا لزم الأمر)

### **❌ مشكلة: الصور لا تظهر**
**السبب**: مجلد images غير مرفوع
**الحل:**
1. تأكد من وجود مجلد `images` في public_html
2. تأكد من وجود ملف `6.jpg` (أيقونة الموقع)

### **❌ مشكلة: الموقع بطيء**
**السبب**: إعدادات الضغط غير مفعلة
**الحل:**
1. ملف .htaccess يحتوي على إعدادات الضغط
2. تأكد من تفعيل mod_deflate في الخادم

### **❌ مشكلة: البيانات لا تحفظ**
**السبب**: localStorage معطل
**الحل:**
- البيانات تحفظ في متصفح المستخدم (localStorage)
- لا تحتاج إعدادات خاصة في الخادم

---

## 📞 **الدعم الفني:**

### **Tarweig Support:**
- **الموقع**: https://tarweig.com
- **البريد**: <EMAIL>
- **الهاتف**: (موجود في حسابك)
- **الدردشة**: من الموقع الرسمي

### **أسئلة شائعة للدعم:**
1. **"أريد تفعيل mod_rewrite لموقع React"**
2. **"أريد تثبيت شهادة SSL مجانية"**
3. **"أريد تفعيل ضغط الملفات (gzip)"**
4. **"أريد زيادة حد رفع الملفات"**

---

## ✅ **قائمة المراجعة النهائية:**

### **قبل الرفع:**
- [ ] تحميل ملف `elhaq-website-tarweig.zip`
- [ ] تسجيل الدخول لـ Tarweig
- [ ] الوصول لـ cPanel

### **أثناء الرفع:**
- [ ] رفع الملف المضغوط
- [ ] استخراج الملفات في public_html
- [ ] حذف الملف المضغوط
- [ ] تحديث إعدادات PHP

### **بعد الرفع:**
- [ ] تفعيل SSL
- [ ] تحديث sitemap.xml
- [ ] اختبار جميع الصفحات
- [ ] اختبار الوظائف
- [ ] فحص الأداء

### **الصيانة:**
- [ ] إعداد النسخ الاحتياطية
- [ ] مراقبة استهلاك الموارد
- [ ] تحديث المحتوى دورياً

---

## 🎯 **معلومات إضافية:**

### **حجم الملفات:**
- **الملف المضغوط**: 6.4 MB
- **بعد الاستخراج**: ~15 MB
- **مناسب للباقة الاقتصادية**: ✅

### **متطلبات الخادم:**
- **PHP**: 8.0+ (مستحسن)
- **Apache**: مع mod_rewrite
- **SSL**: Let's Encrypt (مجاني)
- **قاعدة البيانات**: غير مطلوبة

### **الأداء المتوقع:**
- **سرعة التحميل**: 2-4 ثواني
- **حجم الصفحة**: ~500KB
- **متوافق مع الهواتف**: ✅
- **محسن لمحركات البحث**: ✅

---

## 🚀 **النتيجة النهائية:**

بعد اتباع هذه الخطوات، ستحصل على:

✅ **موقع دعوة الحق مرفوع بالكامل**
✅ **جميع الوظائف تعمل بشكل صحيح**
✅ **أداء عالي وسرعة ممتازة**
✅ **آمن ومحمي بـ SSL**
✅ **متوافق مع جميع الأجهزة**
✅ **محسن لمحركات البحث**
✅ **جاهز للاستخدام الفعلي**

**🎉 موقع دعوة الحق جاهز للانطلاق!**
