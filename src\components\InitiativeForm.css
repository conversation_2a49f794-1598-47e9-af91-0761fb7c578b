/* Initiative Form Styles */
.initiative-form-container {
  background-color: var(--card-bg-color);
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.initiative-form-container h2 {
  margin-top: 0;
  margin-bottom: 1.5rem;
  color: var(--primary-color);
  text-align: right;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  text-align: right;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--input-bg-color);
  color: var(--text-color);
  font-family: inherit;
  transition: border-color 0.3s;
}

.form-group input:focus,
.form-group textarea:focus {
  border-color: var(--primary-color);
  outline: none;
}

.form-group input.error,
.form-group textarea.error {
  border-color: var(--error-color);
}

.error-message {
  color: var(--error-color);
  font-size: 0.875rem;
  margin-top: 0.5rem;
  text-align: right;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
}

.form-actions button {
  min-width: 120px;
}

/* Beneficiaries selection styles */
.beneficiaries-selection {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 0.5rem;
  background-color: var(--input-bg-color);
}

.beneficiaries-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.beneficiary-checkbox {
  display: flex;
  align-items: center;
  padding: 0.5rem;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.beneficiary-checkbox:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.beneficiary-checkbox input[type="checkbox"] {
  width: auto;
  margin-left: 0.75rem;
}

.beneficiary-checkbox label {
  margin-bottom: 0;
  cursor: pointer;
  flex-grow: 1;
}

.selected-count {
  margin-top: 0.5rem;
  font-size: 0.875rem;
  color: var(--text-secondary-color);
  text-align: right;
}

.no-beneficiaries {
  text-align: center;
  padding: 1rem;
  color: var(--text-secondary-color);
}

/* Responsive styles */
@media (max-width: 768px) {
  .initiative-form-container {
    padding: 1rem;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .form-actions button {
    width: 100%;
  }
}
