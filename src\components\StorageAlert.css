/* Storage Alert Styles */
.storage-alert-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  padding: 1rem;
}

.storage-alert {
  background: white;
  border-radius: 12px;
  max-width: 500px;
  width: 100%;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  overflow: hidden;
}

.alert-header {
  background: linear-gradient(135deg, #f39c12, #e67e22);
  color: white;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  position: relative;
}

.alert-icon {
  font-size: 1.5rem;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.alert-header h3 {
  margin: 0;
  flex: 1;
  font-size: 1.2rem;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.3s ease;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.alert-content {
  padding: 1.5rem;
}

.storage-info {
  margin-bottom: 1rem;
}

.usage-bar {
  width: 100%;
  height: 8px;
  background: #ecf0f1;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.usage-fill {
  height: 100%;
  transition: all 0.3s ease;
  border-radius: 4px;
}

.usage-text {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.9rem;
  color: #7f8c8d;
}

.percentage {
  font-weight: 600;
  color: #e74c3c;
}

.alert-message {
  color: #2c3e50;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.alert-actions {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.action-btn {
  flex: 1;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-family: inherit;
}

.quick-clean-btn {
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  color: white;
}

.quick-clean-btn:hover {
  background: linear-gradient(135deg, #229954, #27ae60);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(39, 174, 96, 0.3);
}

.quick-clean-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.migrate-btn {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
}

.migrate-btn:hover {
  background: linear-gradient(135deg, #2980b9, #21618c);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.alert-benefits {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
  border-left: 4px solid #3498db;
}

.alert-benefits h4 {
  margin: 0 0 0.75rem 0;
  color: #2c3e50;
  font-size: 1rem;
}

.alert-benefits ul {
  margin: 0;
  padding-left: 1.5rem;
  list-style: none;
}

.alert-benefits li {
  margin-bottom: 0.5rem;
  color: #34495e;
  font-size: 0.9rem;
  position: relative;
}

.alert-benefits li:before {
  content: '';
  position: absolute;
  left: -1.5rem;
  top: 0.5rem;
  width: 4px;
  height: 4px;
  background: #3498db;
  border-radius: 50%;
}

/* Responsive Design */
@media (max-width: 768px) {
  .storage-alert-overlay {
    padding: 0.5rem;
  }
  
  .storage-alert {
    max-width: 100%;
  }
  
  .alert-header {
    padding: 1rem;
  }
  
  .alert-header h3 {
    font-size: 1.1rem;
  }
  
  .alert-content {
    padding: 1rem;
  }
  
  .alert-actions {
    flex-direction: column;
  }
  
  .action-btn {
    padding: 1rem;
  }
  
  .alert-benefits {
    padding: 0.75rem;
  }
  
  .alert-benefits h4 {
    font-size: 0.9rem;
  }
  
  .alert-benefits li {
    font-size: 0.8rem;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .storage-alert {
    background: #2c3e50;
    color: #ecf0f1;
  }
  
  .alert-message {
    color: #bdc3c7;
  }
  
  .usage-text {
    color: #95a5a6;
  }
  
  .alert-benefits {
    background: #34495e;
    border-left-color: #3498db;
  }
  
  .alert-benefits h4 {
    color: #ecf0f1;
  }
  
  .alert-benefits li {
    color: #bdc3c7;
  }
}
