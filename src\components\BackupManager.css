.backup-manager-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.backup-manager-modal {
  background: white;
  border-radius: 20px;
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  position: relative;
}

/* Header */
.backup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 2rem;
  border-bottom: 2px solid #f0f0f0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 20px 20px 0 0;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.header-icon {
  font-size: 2.5rem;
  color: white;
}

.backup-header h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.8rem;
  font-weight: 700;
}

.backup-header p {
  margin: 0;
  opacity: 0.9;
  font-size: 1rem;
}

.close-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  font-size: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

/* Status Section */
.status-section {
  padding: 2rem;
}

.status-card {
  background: #f8f9fa;
  border-radius: 15px;
  padding: 1.5rem;
  border: 2px solid #e9ecef;
}

.status-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.status-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.3rem;
}

.status-icon {
  font-size: 1.5rem;
}

.status-icon.online {
  color: #27ae60;
}

.status-icon.offline {
  color: #e74c3c;
}

.status-details {
  display: grid;
  gap: 0.75rem;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #dee2e6;
}

.status-item:last-child {
  border-bottom: none;
}

.status-item span:first-child {
  font-weight: 600;
  color: #495057;
}

.status-item span:last-child {
  color: #6c757d;
}

.status-item .online {
  color: #27ae60;
  font-weight: 600;
}

.status-item .offline {
  color: #e74c3c;
  font-weight: 600;
}

/* Actions Section */
.actions-section {
  padding: 0 2rem 2rem 2rem;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  border: 2px solid #e9ecef;
  border-radius: 15px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  text-decoration: none;
  color: inherit;
}

.action-btn:hover:not(:disabled) {
  border-color: #667eea;
  box-shadow: 0 5px 20px rgba(102, 126, 234, 0.2);
  transform: translateY(-2px);
}

.action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.action-btn svg {
  font-size: 1.8rem;
  flex-shrink: 0;
}

.action-btn.sync svg {
  color: #3498db;
}

.action-btn.backup svg {
  color: #27ae60;
}

.action-btn.restore svg {
  color: #f39c12;
}

.action-btn.export svg {
  color: #9b59b6;
}

.action-btn.import svg {
  color: #e67e22;
}

.action-btn h4 {
  margin: 0 0 0.25rem 0;
  font-size: 1.1rem;
  color: #2c3e50;
}

.action-btn p {
  margin: 0;
  font-size: 0.9rem;
  color: #6c757d;
  line-height: 1.4;
}

/* Info Section */
.info-section {
  padding: 0 2rem 2rem 2rem;
}

.info-card {
  display: flex;
  gap: 1rem;
  padding: 1.5rem;
  background: #e8f4fd;
  border-radius: 15px;
  border: 2px solid #bee5eb;
  margin-bottom: 1rem;
}

.info-icon {
  font-size: 1.5rem;
  color: #17a2b8;
  flex-shrink: 0;
  margin-top: 0.25rem;
}

.info-content h4 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
  font-size: 1.1rem;
}

.info-content ul {
  margin: 0;
  padding-right: 1.5rem;
  color: #495057;
}

.info-content li {
  margin-bottom: 0.5rem;
  line-height: 1.5;
}

.last-backup {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #6c757d;
  font-size: 0.9rem;
  justify-content: center;
}

/* Loading Overlay */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 20px;
  z-index: 10;
}

.loading-spinner {
  text-align: center;
  color: #667eea;
}

.loading-spinner svg {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.loading-spinner p {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
}

/* Animations */
.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .backup-manager-overlay {
    padding: 0.5rem;
  }
  
  .backup-manager-modal {
    max-height: 95vh;
  }
  
  .backup-header {
    padding: 1.5rem;
  }
  
  .header-content {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }
  
  .backup-header h2 {
    font-size: 1.5rem;
  }
  
  .status-section,
  .actions-section,
  .info-section {
    padding: 1rem;
  }
  
  .actions-grid {
    grid-template-columns: 1fr;
  }
  
  .action-btn {
    padding: 1rem;
  }
  
  .info-card {
    flex-direction: column;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .backup-header {
    padding: 1rem;
  }
  
  .backup-header h2 {
    font-size: 1.3rem;
  }
  
  .backup-header p {
    font-size: 0.9rem;
  }
  
  .action-btn {
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }
  
  .action-btn svg {
    font-size: 2.5rem;
  }
}
