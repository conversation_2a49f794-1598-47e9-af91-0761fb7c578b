.toast-container {
  position: fixed;
  top: 20px;
  left: 20px;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-width: 350px;
  width: calc(100% - 40px);
}

.toast-notification {
  background-color: var(--card-bg);
  color: var(--text-color);
  border-radius: var(--border-radius);
  padding: 12px 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: space-between;
  overflow: hidden;
  position: relative;
  border-right: 4px solid var(--primary-color);
}

.toast-notification::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 4px;
}

.toast-notification.success {
  border-right-color: var(--success-color);
}

.toast-notification.error {
  border-right-color: var(--error-color);
}

.toast-notification.warning {
  border-right-color: var(--warning-color);
}

.toast-notification.info {
  border-right-color: var(--primary-color);
}

.toast-content {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.toast-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.toast-icon.success {
  color: var(--success-color);
}

.toast-icon.error {
  color: var(--error-color);
}

.toast-icon.warning {
  color: var(--warning-color);
}

.toast-icon.info {
  color: var(--primary-color);
}

.toast-message {
  font-size: 0.95rem;
  line-height: 1.4;
}

.toast-close {
  background: none;
  border: none;
  color: var(--text-secondary-color);
  cursor: pointer;
  font-size: 1rem;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  margin-right: 8px;
  transition: all 0.2s ease;
}

.toast-close:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: var(--text-color);
}

/* Dark theme adjustments */
.dark-theme .toast-notification {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.dark-theme .toast-close:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .toast-container {
    top: 10px;
    left: 10px;
    width: calc(100% - 20px);
  }
  
  .toast-notification {
    padding: 10px 12px;
  }
  
  .toast-icon {
    font-size: 1.2rem;
  }
  
  .toast-message {
    font-size: 0.9rem;
  }
}
