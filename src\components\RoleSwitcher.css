.role-switcher {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin: 2rem 0;
  border: 1px solid #e1e8ed;
}

.role-switcher-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  color: #2c3e50;
}

.role-switcher-header svg {
  font-size: 1.5rem;
  color: #3498db;
}

.role-switcher-header h3 {
  margin: 0;
  font-size: 1.3rem;
  font-weight: 600;
}

.current-role {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 2rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  font-weight: 500;
}

.role-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.9rem;
}

.role-badge.admin {
  background: linear-gradient(135deg, #f39c12, #e67e22);
  color: white;
}

.role-badge.user {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
}

.role-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 2rem;
}

.role-btn {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  border: 2px solid #e1e8ed;
  border-radius: 12px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: inherit;
}

.role-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

.role-btn:disabled {
  cursor: not-allowed;
  opacity: 0.7;
}

.role-btn.active {
  border-color: #3498db;
  background: linear-gradient(135deg, #ebf3fd, #d6eaff);
}

.admin-btn.active {
  border-color: #f39c12;
  background: linear-gradient(135deg, #fef9e7, #fcf3cf);
}

.role-btn svg {
  font-size: 2rem;
  flex-shrink: 0;
}

.admin-btn svg {
  color: #f39c12;
}

.user-btn svg {
  color: #3498db;
}

.role-title {
  font-weight: 600;
  font-size: 1.1rem;
  margin-bottom: 0.25rem;
}

.role-desc {
  font-size: 0.9rem;
  color: #7f8c8d;
}

.role-permissions {
  margin-bottom: 2rem;
}

.role-permissions h4 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.permissions-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.permission-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 6px;
  font-size: 0.9rem;
}

.admin-permission {
  background: linear-gradient(135deg, #fef9e7, #fcf3cf);
  color: #b7950b;
  font-weight: 600;
}

.admin-permission svg {
  color: #f39c12;
}

.role-info {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  border-left: 4px solid #3498db;
}

.role-info h4 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1rem;
}

.info-item {
  margin-bottom: 1.5rem;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item strong {
  display: block;
  margin-bottom: 0.5rem;
  color: #2c3e50;
}

.info-item ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.info-item li {
  padding: 0.25rem 0;
  font-size: 0.9rem;
  color: #34495e;
}

/* Permission denied styles */
.permission-denied {
  padding: 2rem;
  text-align: center;
  background: #fff5f5;
  border: 1px solid #fed7d7;
  border-radius: 8px;
  color: #c53030;
}

.permission-disabled {
  opacity: 0.5;
  cursor: not-allowed !important;
  pointer-events: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .role-switcher {
    padding: 1.5rem;
    margin: 1rem 0;
  }
  
  .role-buttons {
    grid-template-columns: 1fr;
  }
  
  .role-btn {
    padding: 1.25rem;
  }
  
  .role-btn svg {
    font-size: 1.5rem;
  }
  
  .role-info {
    padding: 1rem;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .role-switcher {
    background: #2c3e50;
    border-color: #34495e;
  }
  
  .role-switcher-header {
    color: #ecf0f1;
  }
  
  .current-role {
    background: #34495e;
    color: #ecf0f1;
  }
  
  .role-btn {
    background: #34495e;
    border-color: #4a5f7a;
    color: #ecf0f1;
  }
  
  .role-btn.active {
    background: #4a5f7a;
  }
  
  .permission-item {
    background: #34495e;
    color: #ecf0f1;
  }
  
  .role-info {
    background: #34495e;
    border-left-color: #3498db;
  }
  
  .role-info h4,
  .info-item strong {
    color: #ecf0f1;
  }
  
  .info-item li {
    color: #bdc3c7;
  }
}
