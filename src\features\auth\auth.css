/* Auth Pages Styles */
.auth-container {
  max-width: 450px;
  margin: 2rem auto;
  padding: var(--spacing-lg);
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  box-shadow: 0 4px 12px var(--shadow-color);
}

.auth-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

.auth-header h1 {
  color: var(--primary-color);
  margin-bottom: var(--spacing-sm);
}

.auth-header p {
  color: var(--text-secondary-color);
  margin: 0;
}

.auth-form {
  margin-bottom: var(--spacing-lg);
}

.form-group {
  margin-bottom: var(--spacing-md);
}

.form-group label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: 600;
}

.form-group input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 1rem;
  background-color: var(--bg-color);
  color: var(--text-color);
  transition: border-color 0.3s;
}

.form-group input:focus {
  border-color: var(--primary-color);
  outline: none;
}

.form-group input.error {
  border-color: var(--error-color);
}

.error-message {
  color: var(--error-color);
  font-size: 0.875rem;
  margin-top: var(--spacing-xs);
}

.auth-form button {
  width: 100%;
  padding: 0.75rem;
  margin-top: var(--spacing-md);
  font-size: 1rem;
}

.auth-links {
  text-align: center;
  margin-top: var(--spacing-md);
}

.auth-links a {
  color: var(--primary-color);
  text-decoration: none;
}

.auth-links a:hover {
  text-decoration: underline;
}

.social-login {
  margin-top: var(--spacing-lg);
  text-align: center;
}

.social-login p {
  margin-bottom: var(--spacing-md);
  color: var(--text-secondary-color);
  position: relative;
}

.social-login p::before,
.social-login p::after {
  content: "";
  position: absolute;
  top: 50%;
  width: 30%;
  height: 1px;
  background-color: var(--border-color);
}

.social-login p::before {
  right: 0;
}

.social-login p::after {
  left: 0;
}

.social-buttons {
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
}

.btn-google,
.btn-facebook {
  flex: 1;
  max-width: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem;
  border-radius: 4px;
  font-weight: 600;
  transition: all 0.3s;
}

.btn-google {
  background-color: #DB4437;
  color: white;
}

.btn-facebook {
  background-color: #4267B2;
  color: white;
}

.btn-google:hover {
  background-color: #c53929;
}

.btn-facebook:hover {
  background-color: #365899;
}

/* Responsive styles */
@media (max-width: 768px) {
  .auth-container {
    margin: 1rem;
    padding: var(--spacing-md);
  }
}

@media (max-width: 480px) {
  .social-buttons {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
  
  .btn-google,
  .btn-facebook {
    max-width: 100%;
  }
  
  .auth-header h1 {
    font-size: 1.5rem;
  }
}

