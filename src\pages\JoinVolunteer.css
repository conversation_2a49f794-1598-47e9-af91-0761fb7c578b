.join-volunteer-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 2rem;
}

/* Header Styles */
.join-header {
  text-align: center;
  margin-bottom: 3rem;
}

.header-content {
  background: white;
  padding: 3rem 2rem;
  border-radius: 20px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.header-icon {
  font-size: 4rem;
  color: #667eea;
  margin-bottom: 1rem;
}

.header-content h1 {
  font-size: 2.5rem;
  color: #2c3e50;
  margin: 0 0 1rem 0;
  font-weight: 700;
}

.header-content p {
  font-size: 1.2rem;
  color: #7f8c8d;
  margin: 0;
  line-height: 1.6;
}

/* Progress Bar */
.progress-container {
  background: white;
  padding: 2rem;
  border-radius: 15px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e1e8ed;
  border-radius: 4px;
  margin-bottom: 1rem;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-steps {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.progress-step {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #e1e8ed;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  color: #7f8c8d;
  transition: all 0.3s ease;
}

.progress-step.active {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  transform: scale(1.1);
}

.progress-labels {
  display: flex;
  justify-content: space-between;
  font-size: 0.9rem;
  color: #7f8c8d;
  text-align: center;
}

.progress-labels span {
  flex: 1;
  padding: 0 0.5rem;
}

/* Form Container */
.form-container {
  background: white;
  border-radius: 20px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  padding: 3rem;
  margin-bottom: 3rem;
}

.volunteer-form {
  max-width: 800px;
  margin: 0 auto;
}

.form-step h2 {
  display: flex;
  align-items: center;
  gap: 1rem;
  color: #2c3e50;
  font-size: 1.8rem;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #667eea;
}

.form-step h2 svg {
  color: #667eea;
  font-size: 1.5rem;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  font-weight: 600;
  color: #2c3e50;
  font-size: 1rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 1rem;
  border: 2px solid #e1e8ed;
  border-radius: 10px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: #f8f9fa;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #667eea;
  background: white;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 100px;
}

/* Terms Section */
.terms-section {
  background: #f8f9fa;
  padding: 2rem;
  border-radius: 15px;
  border: 2px solid #e1e8ed;
}

.terms-content h3 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1.3rem;
}

.terms-content ul {
  list-style: none;
  padding: 0;
  margin-bottom: 2rem;
}

.terms-content li {
  padding: 0.5rem 0;
  padding-right: 1.5rem;
  position: relative;
  color: #34495e;
  line-height: 1.6;
}

.terms-content li::before {
  content: '✓';
  position: absolute;
  right: 0;
  color: #27ae60;
  font-weight: bold;
}

.checkbox-group {
  margin-bottom: 2rem;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 1rem;
  cursor: pointer;
  font-size: 1.1rem;
  color: #2c3e50;
  font-weight: 500;
}

.checkbox-label input[type="checkbox"] {
  width: 20px;
  height: 20px;
  margin: 0;
}

.checkmark {
  width: 20px;
  height: 20px;
  border: 2px solid #667eea;
  border-radius: 4px;
  position: relative;
  transition: all 0.3s ease;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
  background: #667eea;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-weight: bold;
}

/* Summary Section */
.summary-section {
  background: white;
  padding: 1.5rem;
  border-radius: 10px;
  border: 1px solid #e1e8ed;
}

.summary-section h3 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1.2rem;
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.summary-item {
  padding: 0.5rem;
  background: #f8f9fa;
  border-radius: 5px;
  font-size: 0.9rem;
}

.summary-item strong {
  color: #667eea;
}

/* Navigation Buttons */
.form-navigation {
  display: flex;
  justify-content: space-between;
  gap: 1rem;
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #e1e8ed;
}

.btn {
  padding: 1rem 2rem;
  border: none;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  min-width: 120px;
  justify-content: center;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-success {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
}

.btn:hover:not(.disabled) {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.btn.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Benefits Section */
.benefits-section {
  background: white;
  padding: 3rem;
  border-radius: 20px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.benefits-section h2 {
  color: #2c3e50;
  font-size: 2rem;
  margin-bottom: 2rem;
  font-weight: 700;
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.benefit-card {
  padding: 2rem;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 15px;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.benefit-card:hover {
  transform: translateY(-5px);
  border-color: #667eea;
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.2);
}

.benefit-icon {
  font-size: 3rem;
  color: #667eea;
  margin-bottom: 1rem;
}

.benefit-card h3 {
  color: #2c3e50;
  font-size: 1.3rem;
  margin-bottom: 1rem;
  font-weight: 600;
}

.benefit-card p {
  color: #7f8c8d;
  line-height: 1.6;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .join-volunteer-container {
    padding: 1rem;
  }
  
  .header-content {
    padding: 2rem 1rem;
  }
  
  .header-content h1 {
    font-size: 2rem;
  }
  
  .progress-container {
    padding: 1rem;
  }
  
  .progress-labels {
    font-size: 0.8rem;
  }
  
  .progress-labels span {
    padding: 0 0.25rem;
  }
  
  .form-container {
    padding: 2rem 1rem;
  }
  
  .form-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .form-navigation {
    flex-direction: column;
  }
  
  .benefits-grid {
    grid-template-columns: 1fr;
  }
  
  .benefit-card {
    padding: 1.5rem;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .join-volunteer-container {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  }
  
  .header-content,
  .progress-container,
  .form-container,
  .benefits-section {
    background: #34495e;
    color: #ecf0f1;
  }
  
  .form-step h2,
  .header-content h1,
  .benefits-section h2 {
    color: #ecf0f1;
  }
  
  .form-group input,
  .form-group select,
  .form-group textarea {
    background: #2c3e50;
    border-color: #4a5f7a;
    color: #ecf0f1;
  }
  
  .terms-section,
  .summary-section {
    background: #2c3e50;
    border-color: #4a5f7a;
  }
}
