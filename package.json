{"name": "<PERSON><PERSON><PERSON>", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "deploy": "npm run build && firebase deploy", "deploy:github": "npm run build && gh-pages -d dist", "firebase:init": "firebase init", "firebase:login": "firebase login"}, "dependencies": {"@reduxjs/toolkit": "^2.0.1", "classnames": "^2.5.1", "file-saver": "^2.0.5", "framer-motion": "^12.11.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "react-redux": "^9.0.4", "react-router-dom": "^6.20.0", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "vite": "^6.3.5"}}