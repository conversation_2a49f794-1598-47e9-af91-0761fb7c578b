/* Image Upload Styles */
.image-upload-container {
  width: 100%;
  margin-bottom: 1rem;
}

.upload-btn {
  width: 100%;
  padding: 0.75rem;
  background-color: var(--bg-color);
  border: 2px dashed var(--border-color);
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
  color: var(--text-color);
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-btn:hover {
  border-color: var(--primary-color);
  background-color: rgba(52, 152, 219, 0.05);
}

.image-preview-container {
  width: 100%;
  position: relative;
}

.image-preview {
  width: 100%;
  max-height: 200px;
  object-fit: contain;
  border-radius: 4px;
  border: 1px solid var(--border-color);
}

.remove-image-btn {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background-color: rgba(231, 76, 60, 0.8);
  color: white;
  border: none;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.3s;
}

.remove-image-btn:hover {
  background-color: rgba(231, 76, 60, 1);
}

.error-message {
  background-color: rgba(231, 76, 60, 0.1);
  border: 1px solid var(--error-color);
  color: var(--error-color);
  padding: 0.5rem;
  border-radius: 4px;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

/* Responsive styles */
@media (max-width: 768px) {
  .image-preview {
    max-height: 150px;
  }
}
