.sync-indicator-container {
  position: relative;
  z-index: 100;
}

.sync-indicator {
  position: relative;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border: 2px solid transparent;
}

/* حالات المزامنة */
.sync-indicator.synced {
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  border-color: #27ae60;
}

.sync-indicator.syncing {
  background: linear-gradient(135deg, #3498db, #5dade2);
  border-color: #3498db;
}

.sync-indicator.offline {
  background: linear-gradient(135deg, #e74c3c, #ec7063);
  border-color: #e74c3c;
}

.sync-indicator:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

/* الأيقونات */
.cloud-icon {
  position: absolute;
  font-size: 1.5rem;
  color: white;
  z-index: 1;
}

.status-icon {
  position: absolute;
  bottom: -2px;
  right: -2px;
  font-size: 0.8rem;
  background: white;
  border-radius: 50%;
  padding: 2px;
  z-index: 2;
}

.status-icon.synced {
  color: #27ae60;
}

.status-icon.syncing {
  color: #3498db;
}

.status-icon.offline {
  color: #e74c3c;
}

/* شارة التغييرات المعلقة */
.pending-badge {
  position: absolute;
  top: -5px;
  left: -5px;
  background: #e74c3c;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: bold;
  border: 2px solid white;
  z-index: 3;
}

/* التولتيب */
.sync-tooltip {
  position: absolute;
  bottom: 60px;
  right: 0;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  border: 1px solid #e1e8ed;
  min-width: 250px;
  z-index: 1000;
  overflow: hidden;
}

.tooltip-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  font-weight: 600;
}

.tooltip-content {
  padding: 1rem;
}

.tooltip-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  font-size: 0.9rem;
}

.tooltip-item:last-child {
  margin-bottom: 0;
}

.tooltip-item span:first-child {
  color: #6c757d;
  font-weight: 500;
}

.tooltip-item span:last-child {
  font-weight: 600;
}

.tooltip-item .online {
  color: #27ae60;
}

.tooltip-item .offline {
  color: #e74c3c;
}

.tooltip-item .syncing {
  color: #3498db;
}

.tooltip-item .synced {
  color: #27ae60;
}

.tooltip-message {
  background: #f8f9fa;
  padding: 0.75rem;
  border-radius: 8px;
  margin-top: 0.75rem;
  font-size: 0.85rem;
  color: #495057;
  border-left: 3px solid #667eea;
}

.tooltip-footer {
  padding: 0.75rem 1rem;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
  font-size: 0.8rem;
  color: #6c757d;
  text-align: center;
}

/* الرسوم المتحركة */
.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* تأثير النبض للمزامنة */
.sync-indicator.syncing::before {
  content: '';
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  border-radius: 50%;
  background: inherit;
  opacity: 0.3;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.1;
  }
  100% {
    transform: scale(1);
    opacity: 0.3;
  }
}

/* تصميم متجاوب */
@media (max-width: 768px) {
  .sync-indicator {
    width: 45px;
    height: 45px;
  }
  
  .cloud-icon {
    font-size: 1.3rem;
  }
  
  .status-icon {
    font-size: 0.7rem;
  }
  
  .pending-badge {
    width: 18px;
    height: 18px;
    font-size: 0.6rem;
  }
  
  .sync-tooltip {
    right: -100px;
    min-width: 220px;
  }
}

@media (max-width: 480px) {
  .sync-indicator {
    width: 40px;
    height: 40px;
  }
  
  .cloud-icon {
    font-size: 1.1rem;
  }
  
  .sync-tooltip {
    right: -150px;
    min-width: 200px;
    bottom: 50px;
  }
  
  .tooltip-content {
    padding: 0.75rem;
  }
  
  .tooltip-item {
    font-size: 0.8rem;
    margin-bottom: 0.5rem;
  }
}

/* وضع الظلام */
@media (prefers-color-scheme: dark) {
  .sync-tooltip {
    background: #2c3e50;
    border-color: #34495e;
    color: #ecf0f1;
  }
  
  .tooltip-content {
    color: #ecf0f1;
  }
  
  .tooltip-item span:first-child {
    color: #bdc3c7;
  }
  
  .tooltip-message {
    background: #34495e;
    color: #ecf0f1;
  }
  
  .tooltip-footer {
    background: #34495e;
    border-top-color: #4a5f7a;
    color: #bdc3c7;
  }
}
